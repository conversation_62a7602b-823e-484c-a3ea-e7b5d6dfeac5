{"name": "Feldera dev", "dockerComposeFile": ["./docker-compose.devcontainer.yml", "../deploy/docker-compose.yml"], "service": "workspace", "runServices": ["<PERSON>pan<PERSON>"], "workspaceFolder": "/workspaces/feldera", "shutdownAction": "stopCompose", "customizations": {"vscode": {"extensions": ["GitHub.vscode-github-actions", "ms-vsliveshare.vsliveshare", "rust-lang.rust-analyzer", "serayuzgur.crates", "vadimcn.vscode-lldb", "mike-co.import-sorter", "lihui.vs-color-picker", "ms-playwright.playwright", "svelte.svelte-vscode", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode"], "settings": {"editor.formatOnSave": true, "terminal.integrated.defaultProfile.linux": "bash", "files.exclude": {"**/CODE_OF_CONDUCT.md": true, "**/LICENSE": true}, "importSorter.generalConfiguration.configurationFilePath": "./import-sorter.json", "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.configPath": "web-console/.prettierrc"}}}, "remoteUser": "user", "mounts": ["source=${localWorkspaceFolder},target=${containerWorkspaceFolder},type=bind"], "postCreateCommand": "bash /workspaces/feldera/.devcontainer/postCreate.sh"}