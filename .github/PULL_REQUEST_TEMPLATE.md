Add a brief description of the pull request.

## Checklist

- [ ] Documentation updated
- [ ] Changelog updated

## Breaking Changes?

Mark if you think the answer is yes for any of these components:

- [ ] OpenAPI / REST HTTP API / feldera-types / manager ([What is a breaking change?](https://github.com/oasdiff/oasdiff/tree/main))
- [ ] Feldera SQL (Syntax, Semantics)
- [ ] feldera-sqllib (incl. dependencies fxp, etc.) ([What is a breaking change?](https://doc.rust-lang.org/cargo/reference/semver.html#semver-compatibility))
- [ ] Python SDK  ([What is a breaking change?](https://peps.python.org/pep-0387/#backwards-compatibility-rules))
- [ ] fda (CLI arguments)
- [ ] Adapters (including configuration)
- [ ] Storage Format / Checkpoints
- [ ] Others (specify)

### Describe Incompatible Changes

Add a few sentences describing the incompatible changes if any.
