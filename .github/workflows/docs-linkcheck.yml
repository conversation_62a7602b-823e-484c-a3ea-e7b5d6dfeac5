name: <PERSON> Checker docs.feldera.com

on:
  schedule:
    - cron: "0 16 * * *"
  workflow_dispatch:

jobs:
  linkcheck:
    runs-on: ubuntu-latest-amd64

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install the default version of uv
        id: setup-uv
        uses: astral-sh/setup-uv@v2

      # ignored crates.io due to https://github.com/rust-lang/crates.io/issues/788
      - name: Check links on feldera.com
        run: |
          uv run --locked linkchecker https://docs.feldera.com --check-extern --no-warnings --ignore-url "https?://localhost|https://crates.io|https://www.linkedin.com|https://ieeexplore.ieee.org|https://x.com|https?://127.0.0.1" --no-robots
        working-directory: docs.feldera.com
