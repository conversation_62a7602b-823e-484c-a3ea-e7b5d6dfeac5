name: Test Java Sources

on:
  workflow_call:

env:
  RUSTC_WRAPPER: sccache
  SCCACHE_CACHE_SIZE: ${{ vars.SCCACHE_CACHE_SIZE }}
  SCCACHE_BUCKET: ${{ vars.SCCACHE_BUCKET }}
  SCCACHE_ENDPOINT: ${{ vars.SCCACHE_ENDPOINT }}
  SCCACHE_REGION: ${{ vars.SCCACHE_REGION }}
  AWS_ACCESS_KEY_ID: "${{ secrets.CI_K8S_MINIO_ACCESS_KEY_ID }}"
  AWS_SECRET_ACCESS_KEY: "${{ secrets.CI_K8S_MINIO_SECRET_ACCESS_KEY }}"

jobs:
  java-tests:
    if: ${{ !contains(vars.CI_SKIP_JOBS, 'java-tests') }}
    name: Execute Java Tests
    strategy:
      matrix:
        include:
          - runner: [k8s-runners-amd64]
          - runner: [k8s-runners-arm64]
    runs-on: ${{ matrix.runner }}

    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - uses: actions/cache@v4
        with:
          path: |
            /home/<USER>/.gradle
            ./sql-to-dbsp-compiler/calcite
          key: sql2dbsp-java-cache2-${{ runner.os }}-${{ hashFiles('sql-to-dbsp-compiler/calcite_version.env') }}
          restore-keys: |
            sql2dbsp-java-cache2-${{ runner.os }}-

      # TODO: Avoid doing this twice and ideally download & reuse the JAR from build-java.yml workflow
      - name: Run build.sh
        run: ./build.sh
        working-directory: ./sql-to-dbsp-compiler
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        env:
          CALCITE_BUILD_DIR: ./calcite
          GRADLE_HOME: /home/<USER>/.gradle

      - name: Stop gradle daemon
        run: gradle --stop

      - name: Run mvn test
        run: mvn test --no-transfer-progress -q -B -pl SQL-compiler -Dsurefire.failIfNoSpecifiedTests=false
        working-directory: ./sql-to-dbsp-compiler
        if: ${{ vars.CI_DRY_RUN != 'true' }}

      - name: Run one quick SLT test
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: mvn test --no-transfer-progress -q -B -Dsurefire.failIfNoSpecifiedTests=false -Dtest=RotateTests#quick
        working-directory: ./sql-to-dbsp-compiler

      - name: Print sccache stats
        run: |
          sccache --show-stats
        if: ${{ vars.CI_DRY_RUN != 'true' }}
