name: Create a new release

on:
  repository_dispatch:
    types: [trigger-oss-release]
  workflow_dispatch:
    inputs:
      sha_to_release:
        description: "SHA to release (don't invoke this manually except to fix release issues)"
        required: true
      version:
        description: "Current version to release"
        required: true

env:
  REGISTRY: ghcr.io

jobs:
  release:
    runs-on: ubuntu-latest-amd64
    steps:
      - name: Generate GitHub App token
        id: app-token
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.CI_ACCESS_APP_ID }}
          private-key: ${{ secrets.CI_ACCESS_APP_PKEY }}
          owner: ${{ github.repository_owner }}
          permission-contents: write

      - name: Set SHA_TO_RELEASE and CURRENT_VERSION
        if: github.event_name == 'repository_dispatch'
        run: |
          echo "Setting version from repository_dispatch"
          echo "CURRENT_VERSION=${{ github.event.client_payload.version }}" >> $GITHUB_ENV
          echo "SHA_TO_RELEASE=${{ github.event.client_payload.sha_to_release }}" >> $GITHUB_ENV

      - name: Set SHA_TO_RELEASE and CURRENT_VERSION
        if: github.event_name == 'workflow_dispatch'
        run: |
          echo "Setting version from workflow_dispatch"
          echo "CURRENT_VERSION=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo "SHA_TO_RELEASE=${{ github.event.inputs.sha_to_release }}" >> $GITHUB_ENV

      - name: Check SHA and version inputs
        run: |
          if [ -z "${SHA_TO_RELEASE}" ] || [ -z "${CURRENT_VERSION}" ]; then
            echo "Missing required release parameters"
            exit 1
          fi

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-tags: true
          ref: ${{ env.SHA_TO_RELEASE }}

      - name: Download artifact
        id: download-artifact
        uses: dawidd6/action-download-artifact@ac66b43f0e6a346234dd65d4d0c8fbb31cb316e5
        with:
          workflow: ci.yml
          workflow_conclusion: success
          commit: ${{ env.SHA_TO_RELEASE }}
          name: feldera-sql-compiler-*|feldera-binaries-*|feldera-docs
          name_is_regexp: true
          skip_unpack: true
          if_no_artifact_found: fail

      - name: Attach version to binaries
        run: |
          mv feldera-binaries-aarch64-unknown-linux-gnu.zip feldera-binaries-v${{ env.CURRENT_VERSION }}-aarch64-unknown-linux-gnu.zip
          mv feldera-binaries-x86_64-unknown-linux-gnu.zip feldera-binaries-v${{ env.CURRENT_VERSION }}-x86_64-unknown-linux-gnu.zip
          unzip -jo feldera-sql-compiler.zip 'sql2dbsp-jar-with-dependencies.jar' -d .
          mv sql2dbsp-jar-with-dependencies.jar sql2dbsp-jar-with-dependencies-v${{ env.CURRENT_VERSION }}.jar
          mv feldera-docs.zip feldera-docs-v${{ env.CURRENT_VERSION }}.zip

      - name: Release on GitHub
        uses: softprops/action-gh-release@72f2c25fcb47643c292f7107632f7a47c1df5cd8
        with:
          target_commitish: ${{ env.SHA_TO_RELEASE }}
          tag_name: v${{ env.CURRENT_VERSION }}
          draft: ${{ vars.RELEASE_DRY_RUN }}
          generate_release_notes: true
          make_latest: true
          files: |
            feldera-binaries-v${{ env.CURRENT_VERSION }}-aarch64-unknown-linux-gnu.zip
            feldera-binaries-v${{ env.CURRENT_VERSION }}-x86_64-unknown-linux-gnu.zip
            sql2dbsp-jar-with-dependencies-v${{ env.CURRENT_VERSION }}.jar
          # A custom token is necessary so the ci-post-release.yml workflow is triggered
          # see also https://github.com/softprops/action-gh-release/issues/59
          token: ${{ steps.app-token.outputs.token }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.SQL2DBSP_S3_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.SQL2DBSP_S3_SECRET_KEY }}
          aws-region: ${{ vars.SQL2DBSP_S3_REGION }}

      - name: Upload JAR to S3 with SHA in filename
        run: |
          JAR_FILE=sql2dbsp-jar-with-dependencies-v${{ env.CURRENT_VERSION }}.jar
          aws s3 cp "$JAR_FILE" "s3://${{ vars.SQL2DBSP_S3_BUCKET }}/$JAR_FILE" \
            --content-type application/java-archive

      # Update docs.feldera.com
      - name: Update docs.feldera.com
        run: unzip feldera-docs-v${{ env.CURRENT_VERSION }}.zip -d docs

      - name: Deploy docs.feldera.com
        if: ${{ vars.RELEASE_DRY_RUN == 'false' }}
        uses: JamesIves/github-pages-deploy-action@6c2d9db40f9296374acc17b90404b6e8864128c8
        with:
          folder: docs
          token: ${{ steps.app-token.outputs.token }}
          branch: ${{ vars.DOCS_PUBLISH_BRANCH }}
          git-config-name: feldera-ci
          git-config-email: <EMAIL>
          repository-name: feldera/docs.feldera.com
          commit-message: Update docs for v${{ env.CURRENT_VERSION }}

      # Publish Docker images
      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: latest

      - name: Tag docker image with version and latest
        if: ${{ vars.RELEASE_DRY_RUN == 'false' }}
        run: |
          docker buildx imagetools create -t ${{ vars.FELDERA_IMAGE_NAME }}:${{ env.CURRENT_VERSION }} ${{ vars.FELDERA_IMAGE_NAME }}:sha-${{ env.SHA_TO_RELEASE }}
          docker buildx imagetools create -t ${{ vars.FELDERA_IMAGE_NAME }}:latest ${{ vars.FELDERA_IMAGE_NAME }}:sha-${{ env.SHA_TO_RELEASE }}
