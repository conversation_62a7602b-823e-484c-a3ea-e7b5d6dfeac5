name: "Docker CI Image"

on:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ghcr.io/feldera/feldera-dev

jobs:
  build-docker-ci-dev:
    name: Build Docker Container used by Actions
    strategy:
      matrix:
        include:
          - runner: [k8s-runners-amd64]
            rust_target: x86_64-unknown-linux-gnu
            docker_arch: amd64
            docker_platform: linux/amd64
          - runner: [k8s-runners-arm64]
            rust_target: aarch64-unknown-linux-gnu
            docker_arch: arm64
            docker_platform: linux/arm64
    runs-on: ${{ matrix.runner }}
    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Prepare Platform Environment Variable
        shell: bash
        run: |
          platform=${{ matrix.docker_platform }}
          echo "PLATFORM_PAIR=${platform//\//-}" >> $GITHUB_ENV

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ${{ env.IMAGE_NAME }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: latest

      - name: Login to GHCR Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push by digest
        id: build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: deploy/build.Dockerfile
          platforms: ${{ matrix.docker_platform }}
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ env.IMAGE_NAME }}
          outputs: type=image,push-by-digest=true,name-canonical=true,push=true
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Export digest
        run: |
          rm -rf $RUNNER_TEMP/digests
          mkdir -p $RUNNER_TEMP/digests
          digest="${{ steps.build.outputs.digest }}"
          touch "$RUNNER_TEMP/digests/${digest#sha256:}"

      - name: Upload digest
        uses: actions/upload-artifact@v4
        with:
          name: digests-${{ env.PLATFORM_PAIR }}
          path: ${{ runner.temp }}/digests/*
          if-no-files-found: error
          retention-days: 7

  # One problem with this workflow is the unknown/unknown architecture
  # it adds in the github UI. It's a github bug:
  # https://github.com/orgs/community/discussions/45969
  merge-manifests:
    name: Merge Docker Manifests
    runs-on: ubuntu-latest-amd64
    needs: build-docker-ci-dev
    steps:
      - name: Download digests
        uses: actions/download-artifact@v4
        with:
          path: ${{ runner.temp }}/digests
          pattern: digests-*
          merge-multiple: true

      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          version: latest

      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            type=sha,format=long

      - name: Create manifest list and push
        working-directory: ${{ runner.temp }}/digests
        run: |
          docker buildx imagetools create $(jq -cr '.tags | map("-t " + .) | join(" ")' <<< "$DOCKER_METADATA_OUTPUT_JSON") \
            $(printf '${{ env.IMAGE_NAME }}@sha256:%s ' *)

      - name: Inspect image
        run: |
          docker buildx imagetools inspect ${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.version }}
