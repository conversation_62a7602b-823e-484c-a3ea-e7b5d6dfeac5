name: Adapter Tests

on:
  workflow_call:
  workflow_dispatch:
    inputs:
      run_id:
        description: "ID of the workflow run that uploaded the artifact"
        required: true

jobs:
  adapter-tests:
    if: ${{ !contains(vars.CI_SKIP_JOBS, 'adapter-tests') }}
    name: Test Adapters
    strategy:
      matrix:
        include:
          - runner: [k8s-runners-amd64]
            arch: x86_64
            target: x86_64-unknown-linux-gnu
          - runner: [k8s-runners-arm64]
            arch: aarch64
            target: aarch64-unknown-linux-gnu
    runs-on: ${{ matrix.runner }}
    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password
          POSTGRES_DB: postgres
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redpanda:
        image: ghcr.io/feldera/redpanda-ci:v24
        options: >-
          --health-cmd "rpk cluster health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
          --cpuset-cpus 0-1
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download Test Binaries
        uses: actions/download-artifact@v4
        with:
          name: feldera-test-binaries-${{ matrix.target }}
          path: build
          run-id: ${{ github.event.inputs.run_id || github.run_id }}
          github-token: ${{ secrets.GITHUB_TOKEN }}

      # Remove if https://github.com/actions/upload-artifact/issues/38 ever gets fixed
      - name: Make binaries executable
        run: chmod +x ./build/*

      - name: Setup uv
        uses: astral-sh/setup-uv@v5
        with:
          version: "0.6.5"

      - name: Install python deps
        run: |
          uv venv
          uv pip install -r ../iceberg/src/test/requirements.ci.txt
        working-directory: crates/adapters

      # Pubsub tests are only supported on x86_64, see below
      - name: Run adapters tests
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: |
          source .venv/bin/activate
          ../../build/dbsp_adapters-* --skip pubsub
        working-directory: crates/adapters
        shell: bash
        env:
          RUST_BACKTRACE: 1
          REDIS_URL: redis://localhost:6379/0
          POSTGRES_URL: postgres://postgres:password@localhost:5432
          REDPANDA_BROKERS: 127.0.0.1
          # This thing can't resolve `localhost` (?), needs to be an IP address
          PUBSUB_EMULATOR_HOST: 127.0.0.1:8681
          ICEBERG_TEST_AWS_ACCESS_KEY_ID: ${{ secrets.delta_table_test_aws_access_key_id }}
          ICEBERG_TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.delta_table_test_aws_secret_access_key }}
          DELTA_TABLE_TEST_AWS_ACCESS_KEY_ID: ${{ secrets.delta_table_test_aws_access_key_id }}
          DELTA_TABLE_TEST_AWS_SECRET_ACCESS_KEY: ${{ secrets.delta_table_test_aws_secret_access_key }}

  # We can't run the pubsub tests on aarch64 because we don't have a
  # arm image for the pubsub emulator:
  # https://github.com/TheKevJames/tools/issues/453
  adapter-tests-pubsub:
    if: ${{ !contains(vars.CI_SKIP_JOBS, 'adapter-tests-pubsub') }}
    name: Test Adapters (PubSub only)
    runs-on: ubuntu-latest-amd64
    services:
      pubsub:
        image: thekevjames/gcloud-pubsub-emulator:e852273e07
        ports:
          - 8681:8681
        options: >-
          -e PUBSUB_PROJECT1=feldera-test,test_pubsub_input-0
          --health-cmd "nc -z 127.0.0.1 8681"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download Test Binaries
        uses: actions/download-artifact@v4
        with:
          name: feldera-test-binaries-x86_64-unknown-linux-gnu
          path: build
          run-id: ${{ github.event.inputs.run_id || github.run_id }}
          github-token: ${{ secrets.GITHUB_TOKEN }}

      # Remove if https://github.com/actions/upload-artifact/issues/38 ever gets fixed
      - name: Make binaries executable
        run: chmod +x ./build/*

      - name: Run adapters pubsub tests
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ../../build/dbsp_adapters-* pubsub
        working-directory: crates/adapters
        env:
          RUST_BACKTRACE: 1
          PUBSUB_EMULATOR_HOST: 127.0.0.1:8681
