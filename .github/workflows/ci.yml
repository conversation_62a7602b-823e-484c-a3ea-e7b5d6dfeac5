name: Merge Queue Tasks

on:
  merge_group:
  workflow_dispatch:

jobs:
  invoke-build-rust:
    name: Build Rust
    uses: ./.github/workflows/build-rust.yml
    secrets: inherit

  invoke-build-java:
    name: Build Java
    uses: ./.github/workflows/build-java.yml
    secrets: inherit

  invoke-build-docs:
    name: Build Docs
    uses: ./.github/workflows/build-docs.yml
    secrets: inherit

  invoke-tests-unit:
    name: Unit Tests
    needs: [invoke-build-rust, invoke-build-java]
    uses: ./.github/workflows/test-unit.yml
    secrets: inherit

  invoke-tests-adapter:
    name: Adapter Tests
    needs: [invoke-build-rust]
    uses: ./.github/workflows/test-adapters.yml
    secrets: inherit

  invoke-build-docker:
    name: Build Docker
    needs: [invoke-build-rust, invoke-build-java]
    uses: ./.github/workflows/build-docker.yml
    secrets: inherit

  invoke-tests-integration-platform:
    name: Integration Tests
    needs: [invoke-build-docker]
    uses: ./.github/workflows/test-integration-platform.yml
    secrets: inherit

  invoke-tests-integration-runtime:
    name: Integration Tests
    needs: [invoke-build-java]
    uses: ./.github/workflows/test-integration-runtime.yml
    secrets: inherit

  invoke-tests-java:
    name: Java Tests
    needs: [invoke-build-java]
    uses: ./.github/workflows/test-java.yml
    secrets: inherit

  # This job needs to be called main (the same as the ci-pre-mergequeue.yml workflow)
  # because of how merge queues work: https://stackoverflow.com/a/78030618
  # and https://github.com/orgs/community/discussions/103114
  main:
    if: always()
    runs-on: ubuntu-latest-amd64
    needs:
      - invoke-build-rust
      - invoke-build-java
      - invoke-build-docs
      - invoke-tests-unit
      - invoke-tests-adapter
      - invoke-build-docker
      - invoke-tests-integration-platform
      - invoke-tests-integration-runtime
      - invoke-tests-java
    steps:
      - name: Finalize Workflow
        run: echo "All tasks completed!"
      - if: ${{ contains(needs.*.result, 'failure') || contains(needs.*.result, 'cancelled') }}
        run: exit 1
