name: Build Rust Sources

on:
  workflow_call:

env:
  CARGO_FLAGS: "--release --locked --all-targets --features pubsub-emulator-test,iceberg-tests-fs,iceberg-tests-glue"
  FELDERA_PLATFORM_VERSION_SUFFIX: ${{ github.sha }}
  RUSTC_WRAPPER: sccache
  SCCACHE_CACHE_SIZE: ${{ vars.SCCACHE_CACHE_SIZE }}
  SCCACHE_BUCKET: ${{ vars.SCCACHE_BUCKET }}
  SCCACHE_ENDPOINT: ${{ vars.SCCACHE_ENDPOINT }}
  SCCACHE_REGION: ${{ vars.SCCACHE_REGION }}
  AWS_ACCESS_KEY_ID: "${{ secrets.CI_K8S_MINIO_ACCESS_KEY_ID }}"
  AWS_SECRET_ACCESS_KEY: "${{ secrets.CI_K8S_MINIO_SECRET_ACCESS_KEY }}"

jobs:
  build-rust:
    name: Build Rust Binaries

    # We run this on two different architectures (x86_64 and aarch64)
    strategy:
      matrix:
        include:
          - runner: [k8s-runners-amd64]
            arch: x86_64
            target: x86_64-unknown-linux-gnu
          - runner: [k8s-runners-arm64]
            arch: aarch64
            target: aarch64-unknown-linux-gnu
    runs-on: ${{ matrix.runner }}

    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Cache Cargo registry and index
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
          key: cargo-registry-${{ runner.os }}-${{ matrix.target }}-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: |
            cargo-registry-${{ runner.os }}-${{ matrix.target }}-

      # Thanks to rust cargo non-sense it's too hard to split this into test job
      #
      # limiting jobs with --test-threads 18: doc test call the linker for every test, on arm machines
      # this can lead up to 128 parallel linkers being used OOM'ing the pod
      # https://github.com/rust-lang/cargo/issues/10702
      - name: Run Rust doc tests
        if: matrix.arch == 'x86_64'
        run: |
          # Don't build the webconsole again for rust tests
          export WEBCONSOLE_BUILD_DIR="$(mktemp -d)"
          touch $WEBCONSOLE_BUILD_DIR/index.html
          cargo test --locked --doc --workspace -- --test-threads 18

      - name: Build Rust binaries
        run: |
          cargo build ${{ env.CARGO_FLAGS }} --target=${{ matrix.target }}

      - name: Print sccache stats
        run: |
          sccache --show-stats

      # Get list of executables
      - name: Collect executables
        id: collect
        run: |
          # Run again with --message-format=json to list out executables
          # (No real recompile since nothing has changed).
          # Then transform newlines to spaces for the artifact step.
          EXES=$(cargo build ${{ env.CARGO_FLAGS }} --target=${{ matrix.target }} --message-format=json \
            | jq -r '.executable | select(. != null)' | tr '\n' ' ')
          echo "Found executables: $EXES"
          # Save it as an output variable for subsequent steps
          echo "executables=$EXES" >> $GITHUB_OUTPUT

      # Copy all executables into a single directory because upload-artifact does not support
      # multiple paths or `|` in glob patterns
      - name: Copy executables
        run: |
          mkdir -p build-artifacts
          for exe in ${{ steps.collect.outputs.executables }}; do
            cp "$exe" build-artifacts/
          done
          mkdir -p build-release-artifacts
          # Move the executables we ship to users to a separate directory
          mv build-artifacts/fda build-release-artifacts/
          mv build-artifacts/pipeline-manager build-release-artifacts/

      # Upload test binaries as one artifact
      - name: Upload test artifacts
        uses: actions/upload-artifact@v4
        with:
          name: feldera-test-binaries-${{ matrix.target }}
          path: build-artifacts
          retention-days: 7

      # Upload binaries to run the product as another artifact
      - name: Upload release artifacts
        uses: actions/upload-artifact@v4
        with:
          name: feldera-binaries-${{ matrix.target }}
          path: build-release-artifacts
          retention-days: 7
