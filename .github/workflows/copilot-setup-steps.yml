name: "Copilot Setup Steps"

on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # The job MUST be called `copilot-setup-steps` or it will not be picked up by Copilot.
  copilot-setup-steps:
    # This doesn't work as usual with new github things
    #container:
    #  image: ghcr.io/feldera/feldera-dev:sha-84a42f92e2687833870d09ad8903b4da263fb77d
    runs-on: ubuntu-latest-amd64
    permissions:
      contents: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install Rust
        uses: dtolnay/rust-toolchain@master
        with:
          toolchain: 1.87.0
      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          version: "0.4.15"
          enable-cache: true
          cache-dependency-glob: "python/uv.lock"
      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version-file: "python/pyproject.toml"
      - name: Set up Java 21
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21
      - name: Install Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.2.2
      - uses: taiki-e/install-action@just
