name: Upload crates.io packages

on:
  workflow_call:
    secrets:
      CARGO_REGISTRY_TOKEN:
        required: true
  workflow_dispatch:

jobs:
  deploy:
    runs-on: [k8s-runners-amd64]
    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87
    steps:
      - uses: actions/checkout@v4

      - name: Run cargo publish
        if: ${{ vars.RELEASE_DRY_RUN == 'false' }}
        run: |
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package feldera-types
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package feldera-storage
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package feldera-rest-api
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package feldera-ir
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package dbsp
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package fda
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package feldera-fxp
          cargo publish ${{ vars.CARGO_PUBLISH_FLAGS }} --package feldera-sqllib
        env:
          CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
