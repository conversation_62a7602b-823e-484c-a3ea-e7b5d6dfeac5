name: Upload Python Package

on:
  workflow_call:
  workflow_dispatch:

permissions:
  contents: read

defaults:
  run:
    shell: bash
    working-directory: ./python

jobs:
  deploy:
    runs-on: ubuntu-latest-amd64

    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          version: "0.4.15"
          enable-cache: true
          cache-dependency-glob: "python/uv.lock"
      - name: "Set up Python"
        uses: actions/setup-python@v5
        with:
          python-version-file: "python/pyproject.toml"
      - name: Install the project
        run: uv sync --all-extras --dev
      - name: Build package
        run: |
          uv build
      - name: Publish package
        if: ${{ vars.RELEASE_DRY_RUN == 'false' }}
        uses: pypa/gh-action-pypi-publish@27b31702a0e7fc50959f5ad993c78deac1bdfc29
        with:
          packages_dir: ./python/dist
          user: __token__
          password: ${{ secrets.PYPI_API_TOKEN }}
      - name: Minimize uv cache
        run: uv cache prune --ci
