name: Java SLT Nightly

on:
  schedule:
    - cron: "30 11 * * *" # 11:30 AM UTC daily

jobs:
  java-tests:
    name: Execute Java SLT
    strategy:
      matrix:
        include:
          - runner: [k8s-runners-amd64]
            arch: x86_64
            target: x86_64-unknown-linux-gnu
          - runner: [k8s-runners-arm64]
            arch: aarch64
            target: aarch64-unknown-linux-gnu
    runs-on: ${{ matrix.runner }}

    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2

      - uses: actions/cache@v4
        with:
          path: |
            /home/<USER>/.gradle
            ./sql-to-dbsp-compiler/calcite
          key: sql2dbsp-java-cache2-${{ runner.os }}-${{ hashFiles('sql-to-dbsp-compiler/calcite_version.env') }}
          restore-keys: |
            sql2dbsp-java-cache2-${{ runner.os }}-

      # TODO: Avoid doing this twice and ideally download & reuse the JAR from build-java.yml workflow
      - name: Run build.sh
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ./build.sh
        working-directory: ./sql-to-dbsp-compiler
        env:
          CALCITE_BUILD_DIR: ./calcite
          GRADLE_HOME: /home/<USER>/.gradle

      - name: Stop gradle daemon
        run: gradle --stop

      - name: Run SLT tests
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: mvn test --no-transfer-progress -q -B -Dsurefire.failIfNoSpecifiedTests=false -Dtest="RotateTests#rotate"
        working-directory: ./sql-to-dbsp-compiler
