name: Post Release Tasks

on:
  release:
    types: [published]

env:
  RUSTC_WRAPPER: sccache
  SCCACHE_CACHE_SIZE: ${{ vars.SCCACHE_CACHE_SIZE }}
  SCCACHE_BUCKET: ${{ vars.SCCACHE_BUCKET }}
  SCCACHE_ENDPOINT: ${{ vars.SCCACHE_ENDPOINT }}
  SCCACHE_REGION: ${{ vars.SCCACHE_REGION }}
  AWS_ACCESS_KEY_ID: "${{ secrets.CI_K8S_MINIO_ACCESS_KEY_ID }}"
  AWS_SECRET_ACCESS_KEY: "${{ secrets.CI_K8S_MINIO_SECRET_ACCESS_KEY }}"

jobs:
  publish-python:
    name: ""
    uses: ./.github/workflows/publish-python.yml
    secrets: inherit
  publish-crates:
    name: ""
    uses: ./.github/workflows/publish-crates.yml
    secrets: inherit

  adjust-versions:
    runs-on: [k8s-runners-amd64]
    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87
    steps:
      - name: Generate GitHub App token
        id: app-token
        uses: actions/create-github-app-token@v2
        with:
          app-id: ${{ vars.CI_ACCESS_APP_ID }}
          private-key: ${{ secrets.CI_ACCESS_APP_PKEY }}
          permission-contents: write

      - uses: actions/checkout@v4
        with:
          # This runs on main because we make the commit on main at the end of the workflow,
          # we use the token so it can circument push to main protection rules
          ref: main
          token: ${{ steps.app-token.outputs.token }}
      - name: Determine current version based on pipeline-manager
        run: |
          echo "CURRENT_VERSION=$(cargo metadata --no-deps | jq -r '.packages[]|select(.name == "pipeline-manager")|.version')" >> $GITHUB_ENV
      - name: Bump cargo versions
        run: |
          cargo set-version --bump ${{ vars.RELEASE_NEXT_VERSION }}
          cargo run --release --locked --bin pipeline-manager -- --dump-openapi
      - name: Determine next version based on pipeline-manager
        run: |
          echo "NEXT_VERSION=$(cargo metadata --no-deps | jq -r '.packages[]|select(.name == "pipeline-manager")|.version')" >> $GITHUB_ENV
      - name: Adjust python version
        working-directory: ./python
        run: |
          sed -i "s/version = \"${{ env.CURRENT_VERSION }}\"/version = \"${{ env.NEXT_VERSION }}\"/g" pyproject.toml
          uv sync
      - name: List changes
        run: |
          git diff
      - uses: EndBug/add-and-commit@a94899bca583c204427a224a7af87c02f9b325d5
        if: ${{ vars.RELEASE_DRY_RUN == 'false' }}
        with:
          message: "ci: Prepare for v${{ env.NEXT_VERSION }}"
          push: origin main
