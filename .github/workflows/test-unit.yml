name: Unit Tests

on:
  workflow_call:
  workflow_dispatch:

env:
  RUST_BACKTRACE: 1

jobs:
  rust-unit-tests:
    name: Rust Unit Tests
    strategy:
      matrix:
        include:
          - runner: [k8s-runners-amd64]
            arch: x86_64
            target: x86_64-unknown-linux-gnu
          - runner: [k8s-runners-arm64]
            arch: aarch64
            target: aarch64-unknown-linux-gnu
    runs-on: ${{ matrix.runner }}

    container:
      image: ghcr.io/feldera/feldera-dev:sha-8781162739b57966b50e89e85730d522d08d3d87

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Download Test Binaries
        uses: actions/download-artifact@v4
        with:
          name: feldera-test-binaries-${{ matrix.target }}
          path: build

      - name: Download Compiler Binaries
        uses: actions/download-artifact@v4
        with:
          name: feldera-sql-compiler
          path: build

      # Remove if https://github.com/actions/upload-artifact/issues/38 ever gets fixed
      - name: Make binaries executable
        run: chmod +x ./build/*

        # Copy the sql2dbsp-jar-with-dependencies.jar to the target directory
        # This is needed for (pipeline-manager) tests
      - name: Copy feldera-sql-compiler JAR
        run: |
          mkdir -p sql-to-dbsp-compiler/SQL-compiler/target
          cp build/sql2dbsp-jar-with-dependencies.jar sql-to-dbsp-compiler/SQL-compiler/target

      - name: Python SDK unit-tests
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: uv run --locked pytest -n ${{ vars.PYTEST_WORKERS }} feldera
        working-directory: python

      - name: feldera-types
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ./build/feldera_types-*

      - name: dbsp
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ./build/dbsp-*

      - name: feldera-fxp
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ./build/feldera_fxp-*

      - name: feldera-sqllib
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ./build/feldera_sqllib-*

      - name: feldera-ir
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ./build/feldera_ir-*

      - name: readers
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ../../../build/readers-*
        # `readers` needs some find files relative to its source directory
        working-directory: sql-to-dbsp-compiler/lib/readers

      - name: dbsp_nexmark
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: ../../build/dbsp_nexmark-*
        # `dbsp_nexmark` needs some find files relative to its source directory
        working-directory: crates/nexmark

      - name: nexmark
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: |
          ../../build/nexmark-* ${{ env.NEXMARK_ARGS }}
          ../../build/nexmark-* ${{ env.NEXMARK_ARGS }} --storage 10000
        working-directory: crates/nexmark
        env:
          NEXMARK_ARGS: "--max-events=1000000 --cpu-cores 8 --num-event-generators 8"

      - name: Run all pipeline_manager-* binaries
        if: ${{ vars.CI_DRY_RUN != 'true' }}
        run: |
          for bin in ../../build/pipeline_manager-*; do
            echo "Running $bin"
            "$bin"
          done
        working-directory: crates/pipeline-manager
