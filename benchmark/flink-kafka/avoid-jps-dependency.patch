This makes it possible for the Nexmark ametrics reporter to work
without the `jps` program from the JDK installed.  We use an upstream
Flink container that doesn't have it available and it's not obvious
how to fix that.

diff --git a/nexmark-flink/src/main/java/com/github/nexmark/flink/metric/cpu/CpuMetricSender.java b/nexmark-flink/src/main/java/com/github/nexmark/flink/metric/cpu/CpuMetricSender.java
index cfbae3f..67a0a8b 100644
--- a/nexmark-flink/src/main/java/com/github/nexmark/flink/metric/cpu/CpuMetricSender.java
+++ b/nexmark-flink/src/main/java/com/github/nexmark/flink/metric/cpu/CpuMetricSender.java
@@ -64,17 +64,22 @@ public class CpuMetricSender implements AutoCloseable {
 		this.interval = interval;
 	}
 
-	public void startClient() throws Exception {
-		List<Integer> taskmanagers = getTaskManagerPidList();
-		if (taskmanagers.isEmpty()) {
-			throw new RuntimeException("There is no Flink TaskManager is running.");
-		}
-		this.serverAddress = InetAddress.getByName(serverHostIp);
-		this.processTrees = new ConcurrentHashMap<>();
-		for (Integer pid : taskmanagers) {
-			processTrees.put(pid, new ProcfsBasedProcessTree(String.valueOf(pid)));
+	public void startClient(int pid) throws Exception {
+	    this.serverAddress = InetAddress.getByName(serverHostIp);
+	    this.processTrees = new ConcurrentHashMap<>();
+		if (pid == 0) {
+			List<Integer> taskmanagers = getTaskManagerPidList();
+			if (taskmanagers.isEmpty()) {
+				throw new RuntimeException("There is no Flink TaskManager is running.");
+			}
+			for (Integer p : taskmanagers) {
+				processTrees.put(p, new ProcfsBasedProcessTree(String.valueOf(p)));
+			}
+			LOG.info("Start to monitor process: {}", taskmanagers);
+		} else {
+			processTrees.put(new Integer(pid), new ProcfsBasedProcessTree(String.valueOf(pid)));
+			LOG.info("Start to monitor process: {}", pid);
 		}
-		LOG.info("Start to monitor process: {}", taskmanagers);
 		this.service.scheduleAtFixedRate(
 			this::reportCpuMetric,
 			0L,
@@ -189,8 +194,12 @@ public class CpuMetricSender implements AutoCloseable {
 		int reporterPort = conf.get(FlinkNexmarkOptions.METRIC_REPORTER_PORT);
 		Duration reportInterval = conf.get(FlinkNexmarkOptions.METRIC_MONITOR_INTERVAL);
 
+		int pid = 0;
+		if (args.length > 0) {
+		    pid = Integer.decode(args[0]).intValue();
+		}
 		CpuMetricSender sender = new CpuMetricSender(reporterAddress, reporterPort, reportInterval);
-		sender.startClient();
+		sender.startClient(pid);
 		sender.service.awaitTermination(Long.MAX_VALUE, TimeUnit.MILLISECONDS);
 	}
 }
diff --git a/nexmark-flink/src/main/resources/bin/metric_client.sh b/nexmark-flink/src/main/resources/bin/metric_client.sh
index 8f06bd0..c08515c 100644
--- a/nexmark-flink/src/main/resources/bin/metric_client.sh
+++ b/nexmark-flink/src/main/resources/bin/metric_client.sh
@@ -34,7 +34,7 @@ case $STARTSTOP in
     (start)
         log=$NEXMARK_LOG_DIR/metric-client.log
         log_setting=(-Dlog.file="$log" -Dlog4j.configuration=file:"$NEXMARK_CONF_DIR"/log4j.properties -Dlog4j.configurationFile=file:"$NEXMARK_CONF_DIR"/log4j.properties)
-        java "${log_setting[@]}" -cp "$NEXMARK_HOME/lib/*:$FLINK_HOME/lib/*" com.github.nexmark.flink.metric.cpu.CpuMetricSender &
+        java "${log_setting[@]}" -cp "$NEXMARK_HOME/lib/*:$FLINK_HOME/lib/*" com.github.nexmark.flink.metric.cpu.CpuMetricSender $(pidof java) &
     ;;
 
     (stop)
