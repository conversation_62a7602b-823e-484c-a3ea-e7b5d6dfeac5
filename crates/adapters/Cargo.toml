[package]
name = "dbsp_adapters"
description = "Data adapters for continuous streaming data analytics with DBSP"
readme = "README.md"
publish = false
categories = ["database", "api-bindings", "network-programming"]
keywords = { workspace = true }
edition = { workspace = true }
version = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }


[features]
default = [
    "with-kafka",
    "with-deltalake",
    "with-iceberg",
    "with-avro",
    "with-nexmark",
    "with-pubsub",
    "with-redis",
]
with-kafka = ["rdkafka"]
with-deltalake = ["deltalake"]
with-iceberg = ["feldera-iceberg"]
with-pubsub = ["google-cloud-pubsub", "google-cloud-gax"]
with-avro = [
    "apache-avro",
    "schema_registry_converter",
    "feldera-adapterlib/with-avro",
]
with-nexmark = ["dbsp_nexmark"]
with-redis = ["redis", "r2d2"]
# Run delta table tests against an S3 bucket.  Requires S3 authentication key
# to be provided via an environment variable.
delta-s3-test = []
# Run Pub/Sub connector tests agains an emulator.
# The emulator must be running. See `pubsub/test.rs`.
pubsub-emulator-test = []
# Run Pub/Sub connector tests agains a GCP account.
# Google Cloud Application Default Credentials (ADC) must be configured. See `pubsub/test.rs`.
pubsub-gcp-test = []
feldera-enterprise = []
iceberg-tests-fs = []
iceberg-tests-glue = []
iceberg-tests-rest = []

[dependencies]
feldera-types = { workspace = true }
feldera-adapterlib = { workspace = true }
feldera-datagen = { workspace = true }
feldera-storage = { workspace = true }
feldera-iceberg = { workspace = true, optional = true }
awc = { workspace = true, features = [
    "compress-gzip",
    "compress-brotli",
    "cookies",
    "rustls-0_23-webpki-roots",
] }
aws-config = { workspace = true }
async-stream = { workspace = true }
anyhow = { workspace = true, features = ["backtrace"] }
bytestring = "1.4.0"
circular-queue = { workspace = true, features = ["serde_support"] }
crossbeam = { workspace = true }
dbsp = { workspace = true }
serde = { workspace = true, features = ["derive", "rc"] }
erased-serde = { workspace = true }
once_cell = { workspace = true }
serde_yaml = { workspace = true }
serde_json = { workspace = true, features = ["raw_value"] }
serde_urlencoded = { workspace = true }
form_urlencoded = { workspace = true }
csv = { workspace = true }
rdkafka = { workspace = true, features = [
    "ssl-vendored",
    "gssapi-vendored",
    "zstd",
    "libz",
], optional = true }
aws-sdk-s3 = { workspace = true, features = ["behavior-version-latest"] }
aws-types = { workspace = true }
actix = { workspace = true }
actix-web = { workspace = true, features = [
    "cookies",
    "macros",
    "compress-gzip",
    "compress-brotli",
    "rustls-0_23",
] }
actix-ws = { workspace = true }
mime = { workspace = true }
size-of = { workspace = true }
futures = { workspace = true }
futures-util = { workspace = true }
proptest = { workspace = true, optional = true }
proptest-derive = { workspace = true, optional = true }
clap = { workspace = true }
tokio = { workspace = true, features = ["sync", "macros", "fs", "rt"] }
utoipa = { workspace = true }
chrono = { workspace = true, features = ["rkyv-64", "serde"] }
colored = { workspace = true }
uuid = { workspace = true, features = ["v4", "std"] }
rustls = { workspace = true }
rkyv = { workspace = true, features = ["std", "size_64"] }
csv-core = { workspace = true }
rand = { workspace = true, features = ["small_rng"] }
tempfile = { workspace = true }
async-trait = { workspace = true }
arrow = { workspace = true, features = ["chrono-tz"] }
parquet = { workspace = true, features = ["json"] }
serde_arrow = { workspace = true }
arrow-json = { workspace = true }
bytes = { workspace = true }
# `datafusion` must be enabled for the writer to implement the `Invariant` feature.
deltalake = { workspace = true, features = [
    "datafusion",
    "s3",
    "gcs",
    "azure",
], optional = true }
apache-avro = { workspace = true, optional = true }
schema_registry_converter = { workspace = true, features = [
    "avro",
    "blocking",
], optional = true }
rust_decimal = { package = "feldera_rust_decimal", version = "1.33.1-feldera.1", features = [
    "tokio-pg",
] }
url = { workspace = true }
ordered-float = { workspace = true }
openssl = { workspace = true }
minitrace = { workspace = true, features = ["enable"] }
minitrace-jaeger = { workspace = true }
atomic = { workspace = true }
dbsp_nexmark = { workspace = true, features = [], optional = true }
enum-map = { workspace = true }
google-cloud-pubsub = { workspace = true, optional = true }
google-cloud-gax = { workspace = true, optional = true }
tokio-util = { workspace = true }
tokio-stream = { workspace = true, features = ["sync"] }
home = { workspace = true }
datafusion = { workspace = true }
sha2 = { workspace = true }
rmp-serde = { workspace = true }
rmpv = { workspace = true, features = ["with-serde"] }
serde_bytes = { workspace = true }
governor = { workspace = true }
nonzero_ext = { workspace = true }
xxhash-rust = { workspace = true, features = ["xxh3"] }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter"] }
comfy-table = { workspace = true }
tokio-postgres = { workspace = true, features = [
    "with-serde_json-1",
    "with-uuid-1",
    "with-chrono-0_4",
] }
num-bigint = { workspace = true }
redis = { workspace = true, features = ["r2d2"], optional = true }
r2d2 = { workspace = true, optional = true }
async-channel = { workspace = true }
threadpool = { workspace = true }
bytemuck = { workspace = true }
num-derive = { workspace = true }
num-traits = { workspace = true } # Used by num-derive derive macro, which cargo-machete can't see
postgres = { workspace = true }
postgres-openssl = { workspace = true }
dyn-clone = { workspace = true }
cpu-time = "1.0.0"
memory-stats = { version = "1.2.0", features = ["always_use_statm"] }
feldera-ir = { workspace = true }
base64 = { workspace = true }
aws-msk-iam-sasl-signer = "1.0.0"
aws-credential-types = "1.2.3"
feldera-sqllib = { workspace = true }
inventory = { workspace = true }
backtrace = { workspace = true }
itertools = { workspace = true }
metrics-process = { version = "2.4.0", default-features = false }
serde_json_path_to_error = { workspace = true }
smallstr = { workspace = true }
dashmap = { workspace = true }
arrow-digest = { workspace = true }
thread-id = { workspace = true }
parking_lot = { workspace = true }

[package.metadata.cargo-machete]
ignored = ["num-traits"]

[target.'cfg(target_os = "linux")'.dependencies]
jemalloc_pprof = { workspace = true, features = ["symbolize"] }

[target.'cfg(unix)'.dependencies]
libc = { workspace = true }
nix = { workspace = true, features = ["signal", "mman", "time", "feature"] }

[dev-dependencies]
actix-test = { workspace = true }
bstr = { workspace = true, features = ["serde1"] }
serde_json = { workspace = true }
size-of = { workspace = true }
tempfile = { workspace = true }
proptest = { workspace = true }
proptest-derive = { workspace = true }
futures = { workspace = true }
bytestring = { workspace = true }
actix-codec = { workspace = true }
async-stream = { workspace = true }
futures-timer = { workspace = true }
test_bin = { workspace = true }
reqwest = { workspace = true, features = ["blocking"] }
serial_test = { workspace = true }
mockall = { workspace = true }
pretty_assertions = { workspace = true }
google-cloud-googleapis = { workspace = true }
postgres-types = { version = "0.2.9", features = ["derive"] }
