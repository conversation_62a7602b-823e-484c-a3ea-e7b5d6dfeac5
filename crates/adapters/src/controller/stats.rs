//! Controller statistics used for reporting and flow control.
//!
//! # Design
//!
//! We use a lock-free design to avoid blocking the datapath.  All
//! performance counters are implemented as atomics.  However, updates
//! across multiple counters are not atomic.  More importantly, performance
//! counters can be slightly out of sync with the actual state of the
//! pipeline.  Such discrepancies are bounded and do _not_ accumulate
//! over time.
//!
//! # Example
//!
//! Consider the following interleaving of three threads: transport
//! endpoint 1, transport endpoint 2, and circuit thread:
// The quotes below are needed because of
// https://github.com/rust-lang/rustfmt/issues/4210
//! ```text
//! | Thread        | Action                              | `buffered_input_records` | actual # of buffered records |
//! | ------------- | ----------------------------------- | :----------------------: | :--------------------------: |
//! | 1. endpoint 1 | enqueues 10 records                 |            10            |              10              |
//! | 2. circuit:   | `ControllerStatus::consume_counters`|             0            |              10              |
//! | 3. endpoint 2 | enqueue 10 records                  |            10            |              20              |
//! | 4. circuit    | circuit.step()                      |            10            |              0               |
//! ```
//!
//! Here endpoint 2 buffers additional records after the
//! `ControllerStatus::buffered_input_records` counter is reset to zero.  As a
//! result, all 20 records enqueued by both endpoints are processed
//! by the circuit, but the counter shows that 10 records are still
//! pending.

use super::{EndpointId, InputEndpointConfig, OutputEndpointConfig};
use crate::{
    controller::{
        checkpoint::{CheckpointInputEndpointMetrics, CheckpointOutputEndpointMetrics},
        journal::{InputChecksums, InputLog},
    },
    PipelineState,
};
use anyhow::anyhow;
use anyhow::Error as AnyError;
use atomic::Atomic;
use base64::{prelude::BASE64_URL_SAFE_NO_PAD, Engine};
use bytemuck::NoUninit;
use chrono::{DateTime, Utc};
use cpu_time::ProcessTime;
use crossbeam::sync::Unparker;
use feldera_adapterlib::{
    errors::journal::ControllerError,
    format::BufferSize,
    transport::{InputReader, Resume},
};
use feldera_types::{
    config::{FtModel, PipelineConfig},
    suspend::SuspendError,
    time_series::SampleStatistics,
    transaction::TransactionId,
};
use memory_stats::memory_stats;
use parking_lot::{RwLock, RwLockReadGuard};
use serde::{Deserialize, Serialize, Serializer};
use std::{
    cmp::min,
    collections::{BTreeMap, BTreeSet, VecDeque},
    sync::{
        atomic::{AtomicBool, AtomicU64, Ordering},
        Mutex,
    },
};
use tokio::sync::broadcast;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use utoipa::ToSchema;

/// Completion token.
///
/// A completion token associated with an endpoint identifies a position in the endpoint's
/// input stream and can be used to check whether all inputs up to this position have been
/// fully processed by the pipeline, including output changes getting processed by all
/// output connectors.
#[derive(Debug, Serialize, Deserialize, ToSchema)]
pub struct CompletionToken {
    /// Pipeline incarnation uuid. Used to reject tokens generated by previous incarnations
    /// of the pipeline (before suspend, crash, or shutdown).
    #[serde(rename = "u")]
    incarnation: Uuid,

    /// Endpoint id.
    #[serde(rename = "e")]
    endpoint_id: EndpointId,

    /// Position the input stream that this token is associated with.
    #[serde(rename = "c")]
    count: u64,
}

impl CompletionToken {
    pub fn new(incarnation: Uuid, endpoint_id: EndpointId, count: u64) -> Self {
        Self {
            incarnation,
            endpoint_id,
            count,
        }
    }

    /// Encode token as a URL-safe based64 string.
    pub fn encode(&self) -> String {
        let json = serde_json::to_string(&self).unwrap();
        BASE64_URL_SAFE_NO_PAD.encode(json)
    }

    /// Decode token from a URL-safe base64 string.
    pub fn decode(token: &str) -> Result<Self, anyhow::Error> {
        let json = BASE64_URL_SAFE_NO_PAD
            .decode(token)
            .map_err(|e| anyhow!("completion token is not a valid base64 string: {e}"))?;
        serde_json_path_to_error::from_slice::<Self>(&json)
            .map_err(|e| anyhow!("error parsing completion token: {e}"))
    }
}

/// Transaction status summarized as a single value.
#[derive(Debug, Default, Copy, PartialEq, Eq, Clone, Serialize, NoUninit)]
#[repr(u8)]
pub enum TransactionStatus {
    #[default]
    NoTransaction,
    TransactionInProgress,
    CommitInProgress,
}

#[derive(Default, Serialize)]
pub struct GlobalControllerMetrics {
    /// State of the pipeline: running, paused, or terminating.
    #[serde(serialize_with = "serialize_atomic")]
    state: Atomic<PipelineState>,

    /// The pipeline has been resumed from a checkpoint and is currently bootstrapping
    /// new and modified views.
    bootstrap_in_progress: AtomicBool,

    #[serde(serialize_with = "serialize_atomic")]
    pub transaction_status: Atomic<TransactionStatus>,

    #[serde(serialize_with = "serialize_atomic")]
    pub transaction_id: Atomic<TransactionId>,

    /// Resident set size of the pipeline process, in bytes.
    // This field is computed on-demand by calling `ControllerStatus::update`.
    pub rss_bytes: AtomicU64,

    /// CPU time used by the pipeline across all threads, in milliseconds.
    // This field is computed on-demand by calling `ControllerStatus::update`.
    pub cpu_msecs: AtomicU64,

    /// Time since the pipeline process started, including time that the
    /// pipeline was running or paused.
    ///
    /// This is the elapsed time since `start_time`.
    // This field is computed on-demand by calling `ControllerStatus::update`.
    pub uptime_msecs: AtomicU64,

    /// Time at which the pipeline process started, in seconds since the epoch.
    #[serde(with = "chrono::serde::ts_seconds")]
    pub start_time: DateTime<Utc>,

    /// Uniquely identifies the pipeline process that started at `start_time`.
    ///
    /// This is a v7 UUID, meaning that they will be ordered by their creation
    /// time (modulo clock skew).
    pub incarnation_uuid: Uuid,

    /// Time at which the pipeline process from which we resumed started, in
    /// seconds since the epoch.
    ///
    /// If this pipeline process was not started from a checkpoint, this is the
    /// same as `start_time`; otherwise, it is earlier.
    #[serde(with = "chrono::serde::ts_seconds")]
    pub initial_start_time: DateTime<Utc>,

    /// Current storage usage in bytes.
    pub storage_bytes: AtomicU64,

    /// Storage usage integrated over time, in megabytes * seconds.
    pub storage_mb_secs: AtomicU64,

    /// Time elapsed while the pipeline is executing a step, multiplied by the
    /// number of foreground and background threads, in milliseconds.
    pub runtime_elapsed_msecs: AtomicU64,

    /// Total number of records currently buffered by all endpoints.
    pub buffered_input_records: AtomicU64,

    /// Total number of bytes currently buffered by all endpoints.
    pub buffered_input_bytes: AtomicU64,

    /// Total number of records received from all endpoints.
    pub total_input_records: AtomicU64,

    /// Total number of bytes received from all endpoints.
    pub total_input_bytes: AtomicU64,

    /// Total number of records pushed to the circuit.
    ///
    /// Equal to `total_input_records - buffered_input_records` in steady state,
    /// but can briefly get out of sync between updates to these two counters.
    /// We track this value for internal use, for when we need an accurate value
    /// and don't want to worry about races, but don't serialize it to avoid
    /// confusing the user with too many related values.
    #[serde(skip)]
    pub total_circuit_input_records: AtomicU64,

    /// Total number of bytes pushed to the circuit.
    ///
    /// Equal to `total_input_bytes - buffered_input_bytes` in steady state.
    #[serde(skip)]
    pub total_circuit_input_bytes: AtomicU64,

    /// Total number of input records processed by the DBSP engine.
    ///
    /// Note that some of the outputs produced for these input records
    /// may still be buffered by output connectors.
    /// Use `total_completed_records` for end-to-end progress tracking.
    pub total_processed_records: AtomicU64,

    /// Total bytes of input records processed by the DBSP engine.
    ///
    /// Note that some of the outputs produced for these input records
    /// may still be buffered by output connectors.
    pub total_processed_bytes: AtomicU64,

    /// Total number of input records processed to completion.
    ///
    /// A record is processed to completion if it has been processed by the DBSP engine and
    /// all outputs derived from it have been processed by all output connectors.
    // This field is computed on-demand by calling `ControllerStatus::update_total_completed_records`.
    pub total_completed_records: AtomicU64,

    /// True if the pipeline has processed all input data to completion.
    /// This means that the following conditions hold:
    ///
    /// * All input endpoints have signalled end-of-input.
    /// * All input records received from all endpoints have been processed by
    ///   the circuit.
    /// * All output records have been sent to respective output transport
    ///   endpoints.
    // This field is computed on-demand by calling `ControllerStatus::update`.
    pub pipeline_complete: AtomicBool,

    /// Forces the controller to perform a step regardless of the state of
    /// input buffers.
    #[serde(skip)]
    pub step_requested: AtomicBool,
}

fn serialize_atomic<S, T>(state: &Atomic<T>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
    T: NoUninit + Serialize,
{
    state.load(Ordering::Acquire).serialize(serializer)
}

impl GlobalControllerMetrics {
    fn new(processed_records: u64, initial_start_time: Option<DateTime<Utc>>) -> Self {
        let start_time = Utc::now();
        let initial_start_time = initial_start_time.unwrap_or(start_time);
        Self {
            state: Atomic::new(PipelineState::Paused),
            bootstrap_in_progress: AtomicBool::new(false),
            transaction_id: Atomic::new(0),
            transaction_status: Atomic::new(TransactionStatus::NoTransaction),
            rss_bytes: AtomicU64::new(0),
            cpu_msecs: AtomicU64::new(0),
            uptime_msecs: AtomicU64::new(0),
            start_time,
            incarnation_uuid: Uuid::now_v7(),
            initial_start_time,
            storage_bytes: AtomicU64::new(0),
            storage_mb_secs: AtomicU64::new(0),
            runtime_elapsed_msecs: AtomicU64::new(0),
            buffered_input_records: AtomicU64::new(0),
            buffered_input_bytes: AtomicU64::new(0),
            total_input_records: AtomicU64::new(processed_records),
            total_input_bytes: AtomicU64::new(0),
            total_circuit_input_records: AtomicU64::new(processed_records),
            total_circuit_input_bytes: AtomicU64::new(0),
            total_processed_records: AtomicU64::new(processed_records),
            total_processed_bytes: AtomicU64::new(0),
            total_completed_records: AtomicU64::new(processed_records),
            pipeline_complete: AtomicBool::new(false),
            step_requested: AtomicBool::new(false),
        }
    }

    pub fn get_state(&self) -> PipelineState {
        self.state.load(Ordering::Acquire)
    }

    fn input_batch(&self, amt: BufferSize) -> u64 {
        self.total_input_bytes
            .fetch_add(amt.bytes as u64, Ordering::Relaxed);
        self.buffered_input_bytes
            .fetch_add(amt.bytes as u64, Ordering::Relaxed);
        self.total_input_records
            .fetch_add(amt.records as u64, Ordering::AcqRel);
        self.buffered_input_records
            .fetch_add(amt.records as u64, Ordering::AcqRel)
    }

    /// `amt` have been pushed to the circuit (but not yet processed, i.e., the
    /// circuit hasn't performed a step yet).
    pub(crate) fn consume_buffered_inputs(&self, amt: BufferSize) {
        self.buffered_input_records
            .fetch_sub(amt.records as u64, Ordering::Release);
        self.total_circuit_input_records
            .fetch_add(amt.records as u64, Ordering::AcqRel);
        self.buffered_input_bytes
            .fetch_sub(amt.bytes as u64, Ordering::Relaxed);
        self.total_circuit_input_bytes
            .fetch_add(amt.bytes as u64, Ordering::Relaxed);
    }

    pub(crate) fn processed_data(&self, amt: BufferSize) -> u64 {
        self.total_processed_bytes
            .fetch_add(amt.bytes as u64, Ordering::AcqRel);
        self.total_processed_records
            .fetch_add(amt.records as u64, Ordering::AcqRel)
            + amt.records as u64
    }

    pub fn rss_bytes(&self) -> u64 {
        self.rss_bytes.load(Ordering::Relaxed)
    }

    pub fn cpu_msecs(&self) -> u64 {
        self.cpu_msecs.load(Ordering::Relaxed)
    }

    pub fn num_buffered_input_records(&self) -> u64 {
        self.buffered_input_records.load(Ordering::Acquire)
    }

    pub fn num_buffered_input_bytes(&self) -> u64 {
        self.buffered_input_bytes.load(Ordering::Acquire)
    }

    pub fn num_total_input_records(&self) -> u64 {
        self.total_input_records.load(Ordering::Acquire)
    }

    pub fn num_total_input_bytes(&self) -> u64 {
        self.total_input_bytes.load(Ordering::Acquire)
    }

    pub fn num_total_circuit_input_records(&self) -> u64 {
        self.total_circuit_input_records.load(Ordering::Acquire)
    }

    pub fn num_total_processed_records(&self) -> u64 {
        self.total_processed_records.load(Ordering::Acquire)
    }

    pub fn num_total_processed_bytes(&self) -> u64 {
        self.total_processed_bytes.load(Ordering::Acquire)
    }

    fn unset_step_requested(&self) -> bool {
        self.step_requested.swap(false, Ordering::Acquire)
    }

    fn bootstrap_in_progress(&self) -> bool {
        self.bootstrap_in_progress.load(Ordering::Acquire)
    }

    fn set_bootstrap_in_progress(&self, bootstrap_in_progress: bool) {
        self.bootstrap_in_progress
            .store(bootstrap_in_progress, Ordering::Release);
    }

    fn set_step_requested(&self) -> bool {
        self.step_requested.swap(true, Ordering::AcqRel)
    }
}

// `ShardedLock` is a read/write lock optimized for fast reads.
// Write access is only required when adding or removing an endpoint.
// Regular stats updates only require a read lock thanks to the use of
// atomics.
type InputsStatus = RwLock<BTreeMap<EndpointId, InputEndpointStatus>>;
type OutputsStatus = RwLock<BTreeMap<EndpointId, OutputEndpointStatus>>;

// Serialize inputs as a vector of `InputEndpointStatus`.
fn serialize_inputs<S>(inputs: &InputsStatus, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let inputs = inputs.read_recursive();
    let mut inputs = inputs.values().collect::<Vec<_>>();
    inputs.sort_by(|ep1, ep2| ep1.endpoint_name.cmp(&ep2.endpoint_name));
    inputs.serialize(serializer)
}

// Serialize outputs as a vector of `InputEndpointStatus`.
fn serialize_outputs<S>(outputs: &OutputsStatus, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    let outputs = outputs.read_recursive();
    let mut outputs = outputs.values().collect::<Vec<_>>();
    outputs.sort_by(|ep1, ep2| ep1.endpoint_name.cmp(&ep2.endpoint_name));
    outputs.serialize(serializer)
}

/// Controller statistics.
#[derive(Serialize)]
pub struct ControllerStatus {
    /// Global controller configuration.
    #[serde(skip_serializing)]
    pub pipeline_config: PipelineConfig,

    /// Global controller metrics.
    pub global_metrics: GlobalControllerMetrics,

    /// Statistics time series.
    #[serde(skip_serializing)]
    pub time_series: Mutex<VecDeque<SampleStatistics>>,

    /// Broadcast channel for notifying subscribers about new time series data.
    #[serde(skip_serializing)]
    pub time_series_notifier: broadcast::Sender<SampleStatistics>,

    /// If this is empty, the pipeline can be suspended or checkpointed.  If
    /// this is nonempty, it is the (permanent or temporary) reasons why not.
    // This field is computed on-demand by calling `ControllerStatus::update`.
    pub suspend_error: Mutex<Option<SuspendError>>,

    /// Input endpoint configs and metrics.
    #[serde(serialize_with = "serialize_inputs")]
    pub(crate) inputs: InputsStatus,

    /// Output endpoint configs and metrics.
    #[serde(serialize_with = "serialize_outputs")]
    outputs: OutputsStatus,
}

impl ControllerStatus {
    pub fn new(
        pipeline_config: PipelineConfig,
        processed_records: u64,
        initial_start_time: Option<DateTime<Utc>>,
    ) -> Self {
        let (time_series_notifier, _) = broadcast::channel(1024); // Buffer for up to 1024 time series updates
        Self {
            pipeline_config,
            global_metrics: GlobalControllerMetrics::new(processed_records, initial_start_time),
            time_series: Mutex::new(VecDeque::with_capacity(60)),
            time_series_notifier,
            suspend_error: Mutex::new(None),
            inputs: RwLock::new(BTreeMap::new()),
            outputs: RwLock::new(BTreeMap::new()),
        }
    }

    pub fn state(&self) -> PipelineState {
        self.global_metrics.state.load(Ordering::Relaxed)
    }

    /// Set the state to `desired`.
    ///
    /// Setting the state to [PipelineState::Terminated] is permanent; the state
    /// can't be re-set to any other state after that.
    pub fn set_state(&self, desired: PipelineState) {
        if desired == PipelineState::Terminated {
            self.global_metrics.state.store(desired, Ordering::Relaxed);
        } else {
            let mut current = self.state();
            while current != desired && current != PipelineState::Terminated {
                match self.global_metrics.state.compare_exchange(
                    current,
                    desired,
                    Ordering::Relaxed,
                    Ordering::Relaxed,
                ) {
                    Ok(_) => break,
                    Err(changed) => current = changed,
                }
            }
        }
    }

    /// Lookup input endpoint by name.
    pub fn input_endpoint_id_by_name(
        &self,
        endpoint_name: &str,
    ) -> Result<EndpointId, ControllerError> {
        let inputs = self.input_status();

        for (endpoint_id, descr) in inputs.iter() {
            if descr.endpoint_name == endpoint_name {
                return Ok(*endpoint_id);
            }
        }

        Err(ControllerError::unknown_input_endpoint(endpoint_name))
    }

    /// Lookup output endpoint by name.
    pub fn output_endpoint_id_by_name(
        &self,
        endpoint_name: &str,
    ) -> Result<EndpointId, ControllerError> {
        let outputs = self.output_status();

        for (endpoint_id, descr) in outputs.iter() {
            if descr.endpoint_name == endpoint_name {
                return Ok(*endpoint_id);
            }
        }

        Err(ControllerError::unknown_output_endpoint(endpoint_name))
    }

    /// Sets `endpoint`'s paused state to `paused` and returns whether it was
    /// previously paused.
    pub fn set_input_endpoint_paused(&self, endpoint: &EndpointId, paused: bool) -> Option<bool> {
        Some(
            self.input_status()
                .get(endpoint)?
                .paused
                .swap(paused, Ordering::Release),
        )
    }

    /// Pauses `endpoint` and returns whether the endpoint was previously
    /// paused.
    pub fn pause_input_endpoint(&self, endpoint: &EndpointId) -> Option<bool> {
        self.set_input_endpoint_paused(endpoint, true)
    }

    /// Unpauses `endpoint` and returns whether the endpoint was previously
    /// paused.
    pub fn start_input_endpoint(&self, endpoint: &EndpointId) -> Option<bool> {
        self.set_input_endpoint_paused(endpoint, false)
    }

    pub fn is_input_endpoint_paused(&self, endpoint: &EndpointId) -> Option<bool> {
        Some(self.input_status().get(endpoint)?.is_paused_by_user())
    }

    /// Invoked when one of the input endpoints has finished processing
    /// to check if any paused endpoints have all their 'start_after'
    /// dependencies satisfied and can be started.
    pub fn start_dependencies(&self, backpressure_thread_unparker: &Unparker) {
        let mut endpoints_to_start: Vec<EndpointId> = Vec::new();

        let inputs = self.input_status();

        let mut unfinished_labels = BTreeSet::new();
        for input in inputs.values() {
            if !input.finished() {
                unfinished_labels.extend(input.config.connector_config.labels.iter());
            }
        }

        for (endpoint_id, input) in inputs.iter() {
            if !input.paused.load(Ordering::Acquire) {
                continue;
            }
            if let Some(start_after) = &input.config.connector_config.start_after {
                if start_after
                    .iter()
                    .all(|label| !unfinished_labels.contains(label))
                {
                    info!("starting endpoint {}, whose 'start_after' dependencies have been satisfied", input.endpoint_name);
                    endpoints_to_start.push(*endpoint_id);
                }
            }
        }

        drop(inputs);

        for endpoint_id in endpoints_to_start.iter() {
            self.start_input_endpoint(endpoint_id);
        }

        if !endpoints_to_start.is_empty() {
            backpressure_thread_unparker.unpark();
        }
    }

    pub fn remove_input(&self, endpoint_id: &EndpointId) -> Option<InputEndpointStatus> {
        self.inputs.write().remove(endpoint_id)
    }

    pub fn remove_output(&self, endpoint_id: &EndpointId) {
        self.outputs.write().remove(endpoint_id);
    }

    /// Initialize stats for a new output endpoint.
    pub fn add_output(
        &self,
        endpoint_id: &EndpointId,
        endpoint_name: &str,
        config: &OutputEndpointConfig,
        initial_statistics: Option<&CheckpointOutputEndpointMetrics>,
    ) {
        // Initialize the `total_processed_input_records` counter on the new endpoint to `total_processed_records`:
        // logically the new endpoint is up to speed with the outputs produced by the pipeline so far and only needs to
        // process any future outputs.
        let total_processed_records = self
            .global_metrics
            .total_processed_records
            .load(Ordering::Acquire);
        self.outputs.write().insert(
            *endpoint_id,
            OutputEndpointStatus::new(
                endpoint_name,
                config,
                total_processed_records,
                initial_statistics,
            ),
        );
    }

    /// Total number of records currently buffered by all input endpoints.
    pub fn num_buffered_input_records(&self) -> u64 {
        self.global_metrics.num_buffered_input_records()
    }

    /// Total number of bytes currently buffered by all input endpoints.
    pub fn num_buffered_input_bytes(&self) -> u64 {
        self.global_metrics.num_buffered_input_bytes()
    }

    /// Total number of records received from all input endpoints.
    pub fn num_total_input_records(&self) -> u64 {
        self.global_metrics.num_total_input_records()
    }

    /// Total number of bytes received from all input endpoints.
    pub fn num_total_input_bytes(&self) -> u64 {
        self.global_metrics.num_total_input_bytes()
    }

    pub fn num_total_circuit_input_records(&self) -> u64 {
        self.global_metrics.num_total_circuit_input_records()
    }

    pub fn num_total_processed_records(&self) -> u64 {
        self.global_metrics.num_total_processed_records()
    }

    pub fn num_total_processed_bytes(&self) -> u64 {
        self.global_metrics.num_total_processed_bytes()
    }

    pub fn unset_step_requested(&self) -> bool {
        self.global_metrics.unset_step_requested()
    }

    pub fn bootstrap_in_progress(&self) -> bool {
        self.global_metrics.bootstrap_in_progress()
    }

    pub fn set_bootstrap_in_progress(&self, bootstrap_in_progress: bool) {
        self.global_metrics
            .set_bootstrap_in_progress(bootstrap_in_progress);
    }

    pub fn transaction_in_progress(&self) -> bool {
        self.global_metrics
            .transaction_status
            .load(Ordering::Acquire)
            != TransactionStatus::NoTransaction
    }

    pub fn request_step(&self, circuit_thread_unparker: &Unparker) {
        let old = self.global_metrics.set_step_requested();
        if !old {
            circuit_thread_unparker.unpark();
        }
    }

    /// Input endpoint stats.
    pub fn input_status(&self) -> RwLockReadGuard<BTreeMap<EndpointId, InputEndpointStatus>> {
        self.inputs.read_recursive()
    }

    /// Output endpoint stats.
    pub fn output_status(&self) -> RwLockReadGuard<BTreeMap<EndpointId, OutputEndpointStatus>> {
        self.outputs.read_recursive()
    }

    /// Number of records buffered by the endpoint or 0 if the endpoint
    /// doesn't exist (the latter is possible if the endpoint is being
    /// destroyed).
    pub fn num_input_endpoint_buffered_records(&self, endpoint_id: &EndpointId) -> u64 {
        match &self.input_status().get(endpoint_id) {
            None => 0,
            Some(endpoint_stats) => endpoint_stats
                .metrics
                .buffered_records
                .load(Ordering::Acquire),
        }
    }

    pub fn num_total_completed_records(&self) -> u64 {
        self.global_metrics
            .total_completed_records
            .load(Ordering::Acquire)
    }

    /// Update `global_metrics.total_completed_records`.
    ///
    /// Must be invoked any time this metric can change, i.e., after every output
    /// produced by an output connector as well as after each step.
    ///
    /// Computes `total_completed_records` as the minimum `total_processed_input_records` across
    /// all output connectors. In case there are no output connectors attached to the pipeline,
    /// returns `total_processed_records`.
    pub fn update_total_completed_records(&self) {
        let mut total_completed_records = self
            .global_metrics
            .total_processed_records
            .load(Ordering::Acquire);

        for output_ep in self.output_status().values() {
            total_completed_records = min(
                total_completed_records,
                output_ep.num_total_processed_input_records(),
            );
        }

        self.global_metrics
            .total_completed_records
            .store(total_completed_records, Ordering::Release);
    }

    /// Generate completion token for an endpoint based on the current number of records ingested by this endpoint.
    pub fn completion_token(
        &self,
        endpoint_name: &str,
        endpoint_id: EndpointId,
    ) -> Result<CompletionToken, ControllerError> {
        if let Some(endpoint_stats) = self.input_status().get(&endpoint_id) {
            let num_input_records = endpoint_stats.completion_token(
                self.num_total_circuit_input_records(),
                self.num_total_completed_records(),
            );
            Ok(CompletionToken::new(
                self.global_metrics.incarnation_uuid,
                endpoint_id,
                num_input_records,
            ))
        } else {
            Err(ControllerError::unknown_input_endpoint(endpoint_name))
        }
    }

    /// Check completion status of a token.
    pub fn completion_status(
        &self,
        completion_token: &CompletionToken,
    ) -> Result<bool, ControllerError> {
        if completion_token.incarnation != self.global_metrics.incarnation_uuid {
            return Err(ControllerError::pipeline_restarted(&format!(
                "Completion token was created by a previous incarnation of the pipeline (incarnation uuid: {}) and is not valid for the current incarnation ({}). This indicates that the pipeline was suspended and resumed from a checkpoint or restarted after a failure.",
                &completion_token.incarnation,
                &self.global_metrics.incarnation_uuid
            )));
        }

        if let Some(endpoint_stats) = self.input_status().get(&completion_token.endpoint_id) {
            Ok(endpoint_stats
                .completion_status(completion_token.count, self.num_total_completed_records()))
        } else {
            Err(ControllerError::unknown_endpoint_in_completion_token(
                completion_token.endpoint_id,
            ))
        }
    }

    /// Update the global counters after receiving a new input batch.
    ///
    /// This method is used for inserts that don't belong to an endpoint, e.g.,
    /// happen by executing an ad-hoc INSERT query.
    pub(super) fn input_batch_global(&self, amt: BufferSize, circuit_thread_unparker: &Unparker) {
        let num_records = amt.records as u64;
        // Increment buffered_records; unpark circuit thread once
        // `min_batch_size_records` is exceeded.
        let old = self.global_metrics.input_batch(amt);

        if old == 0
            || (old <= self.pipeline_config.global.min_batch_size_records
                && old + num_records > self.pipeline_config.global.min_batch_size_records)
        {
            circuit_thread_unparker.unpark();
        }
    }

    /// Update counters after receiving a new input batch.
    ///
    /// # Arguments
    ///
    /// * `endpoint_id` - id of the input endpoint.
    /// * `num_bytes` - number of bytes received.
    /// * `num_records` - number of records in the deserialized batch.
    /// * `global_config` - global controller config.
    /// * `circuit_thread_unparker` - unparker used to wake up the circuit
    ///   thread if the total number of buffered records exceeds
    ///   `min_batch_size_records`.
    /// * `backpressure_thread_unparker` - unparker used to wake up the
    ///   backpressure thread if the endpoint is full.
    pub(super) fn input_batch_from_endpoint(
        &self,
        endpoint_id: EndpointId,
        amt: BufferSize,
        backpressure_thread_unparker: &Unparker,
    ) {
        // There is a potential race condition if the endpoint is currently
        // being removed. In this case, it's safe to ignore this operation.
        if !amt.is_empty() {
            let inputs = self.input_status();
            if let Some(endpoint_stats) = inputs.get(&endpoint_id) {
                let old = endpoint_stats.add_buffered(amt);
                let threshold = endpoint_stats.config.connector_config.max_queued_records;
                if old < threshold && old + amt.records as u64 >= threshold {
                    backpressure_thread_unparker.unpark();
                }
            }
        }
    }

    /// Update counters after receiving an end-of-input event on an input
    /// endpoint.
    ///
    /// # Arguments
    ///
    /// * `endpoint_id` - id of the input endpoint.
    /// * `num_records` - number of records returned by `Parser::eoi`.
    /// * `circuit_thread_unparker` - unparker used to wake up the circuit
    ///   thread if the total number of buffered records exceeds
    ///   `min_batch_size_records`.
    pub fn eoi(
        &self,
        endpoint_id: EndpointId,
        circuit_thread_unparker: &Unparker,
        backpressure_thread_unparker: &Unparker,
    ) {
        if self.global_metrics.num_buffered_input_records() == 0 {
            circuit_thread_unparker.unpark();
        }

        let mut finished = false;

        // Update endpoint counters, no need to wake up the backpressure thread since we
        // won't see any more inputs from this endpoint.
        let inputs = self.input_status();
        if let Some(endpoint_stats) = inputs.get(&endpoint_id) {
            endpoint_stats.eoi();
            finished = endpoint_stats.finished();
        };

        drop(inputs);

        // Check if we can start any paused endpoints waiting for this endpoint to finish.
        // It's possible that the endpoint is at the end of input, but not all of its updates
        // have been processed yet. In this case, `finished` will be `false`, and we will call
        // `start_dependencies` when the endpoint is finished in the `completed()` method below.
        if finished {
            self.start_dependencies(backpressure_thread_unparker);
        }
    }

    /// Endpoint pushed additional records to the circuit.
    pub fn extended(
        &self,
        endpoint_id: EndpointId,
        step_results: StepResults,
        backpressure_thread_unparker: &Unparker,
    ) {
        let inputs = self.input_status();
        self.global_metrics
            .consume_buffered_inputs(step_results.amt);

        let mut finished = false;

        if let Some(endpoint_stats) = inputs.get(&endpoint_id) {
            endpoint_stats.extended(
                step_results,
                self.num_total_circuit_input_records(),
                self.num_total_completed_records(),
            );
            finished = endpoint_stats.finished();
        };

        drop(inputs);

        // Check if we can start any paused endpoints waiting for this endpoint to finish.
        if finished {
            self.start_dependencies(backpressure_thread_unparker);
        }
    }

    pub fn enqueue_batch(&self, endpoint_id: EndpointId, num_records: usize) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            endpoint_stats.enqueue_batch(num_records);
        }
    }

    pub fn buffer_batch(
        &self,
        endpoint_id: EndpointId,
        num_records: usize,
        circuit_thread_unparker: &Unparker,
    ) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            let old = endpoint_stats.buffer_batch(num_records);
            if old - (num_records as u64)
                <= endpoint_stats.config.connector_config.max_queued_records
                && old >= endpoint_stats.config.connector_config.max_queued_records
            {
                circuit_thread_unparker.unpark();
            }
        };
    }

    /// `total_processed_records` is the total number of records processed by the circuit
    /// before this output batch was produced or `None` if the circuit is executing a transaction.
    pub fn output_batch(
        &self,
        endpoint_id: EndpointId,
        total_processed_records: Option<u64>,
        num_records: usize,
        circuit_thread_unparker: &Unparker,
    ) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            let old = endpoint_stats.output_batch(total_processed_records, num_records);
            self.update_total_completed_records();

            let new = old - (num_records as u64);
            let threshold = endpoint_stats.config.connector_config.max_queued_records;
            if (old >= threshold && new <= threshold) || new == 0 {
                circuit_thread_unparker.unpark();
            }
        };
    }

    pub fn output_buffered_batches(&self, endpoint_id: EndpointId, total_processed_records: u64) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            endpoint_stats.output_buffered_batches(total_processed_records);
        }
    }

    pub fn output_buffer(&self, endpoint_id: EndpointId, num_bytes: usize, num_records: usize) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            endpoint_stats.output_buffer(num_bytes, num_records);
        };
    }

    pub fn output_buffers_full(&self) -> bool {
        self.output_status().values().any(|endpoint_stats| {
            let num_buffered_records = endpoint_stats
                .metrics
                .queued_records
                .load(Ordering::Acquire);
            num_buffered_records >= endpoint_stats.config.connector_config.max_queued_records
        })
    }

    pub fn parse_error(&self, endpoint_id: EndpointId) {
        if let Some(endpoint_stats) = self.input_status().get(&endpoint_id) {
            endpoint_stats.parse_error();
        }
    }

    pub fn encode_error(&self, endpoint_id: EndpointId) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            endpoint_stats.encode_error();
        }
    }

    pub fn input_transport_error(&self, endpoint_id: EndpointId, fatal: bool, error: &AnyError) {
        if let Some(endpoint_stats) = self.input_status().get(&endpoint_id) {
            endpoint_stats.transport_error(fatal, error);
        }
    }

    pub fn output_transport_error(&self, endpoint_id: EndpointId, fatal: bool, error: &AnyError) {
        if let Some(endpoint_stats) = self.output_status().get(&endpoint_id) {
            endpoint_stats.transport_error(fatal, error);
        }
    }

    /// True if the pipeline has processed all inputs to completion.
    pub fn pipeline_complete(&self) -> bool {
        // All input endpoints (if any) are at end of input.
        if !self
            .input_status()
            .values()
            .filter(|endpoint_stats| !endpoint_stats.endpoint_name.starts_with("api-ingress"))
            .all(|endpoint_stats| endpoint_stats.is_eoi())
        {
            return false;
        }

        // All received records have been processed by the circuit.
        let total_input_records = self.num_total_input_records();

        if self.num_total_processed_records() != total_input_records {
            return false;
        }

        // Outputs have been pushed to their respective transport endpoints.
        if !self.output_status().values().all(|endpoint_stats| {
            endpoint_stats.num_total_processed_input_records() == total_input_records
        }) {
            return false;
        }

        true
    }

    pub fn update(&self, suspend_error: Option<SuspendError>) {
        self.global_metrics
            .pipeline_complete
            .store(self.pipeline_complete(), Ordering::Release);

        *self.suspend_error.lock().unwrap() = suspend_error;

        let uptime = Utc::now() - self.global_metrics.start_time;
        self.global_metrics.uptime_msecs.store(
            uptime.num_milliseconds().try_into().unwrap_or(0),
            Ordering::Relaxed,
        );

        if let Some(usage) = memory_stats() {
            self.global_metrics
                .rss_bytes
                .store(usage.physical_mem as u64, Ordering::Relaxed);
        } else {
            error!("Failed to fetch process RSS");
        }

        match ProcessTime::try_now() {
            Ok(time) => {
                self.global_metrics
                    .cpu_msecs
                    .store(time.as_duration().as_millis() as u64, Ordering::Relaxed);
            }
            Err(e) => {
                error!("Failed to fetch process times: {e}");
            }
        }
    }
}

#[derive(Default, Serialize)]
pub struct InputEndpointMetrics {
    /// Total bytes pushed to the endpoint since it was created.
    pub total_bytes: AtomicU64,

    /// Total records pushed to the endpoint since it was created.
    pub total_records: AtomicU64,

    /// Number of records currently buffered by the endpoint
    /// (not yet consumed by the circuit).
    pub buffered_records: AtomicU64,

    /// Number of bytes currently buffered by the endpoint
    /// (not yet consumed by the circuit).
    pub buffered_bytes: AtomicU64,

    /// Number of records pushed from the endpoint's queue to the circuit.
    ///
    /// Equal to `total_records - buffered_records` in steady state.
    #[serde(skip)]
    pub circuit_input_records: AtomicU64,

    /// Number of bytes pushed from the endpoint's queue to the circuit.
    ///
    /// Equal to `total_bytes - buffered_bytes` in steady state.
    #[serde(skip)]
    pub circuit_input_bytes: AtomicU64,

    pub num_transport_errors: AtomicU64,

    pub num_parse_errors: AtomicU64,

    pub end_of_input: AtomicBool,
}

pub struct StepResults {
    pub amt: BufferSize,
    pub resume: Option<Resume>,
}

#[derive(Debug)]
pub struct MissingReplay;

impl TryFrom<StepResults> for InputLog {
    type Error = MissingReplay;

    fn try_from(results: StepResults) -> Result<Self, Self::Error> {
        match results.resume {
            Some(Resume::Replay { seek, replay, hash }) => Ok(InputLog {
                data: replay,
                metadata: seek,
                checksums: InputChecksums {
                    num_records: results.amt.records as u64,
                    hash,
                },
            }),
            _ => Err(MissingReplay),
        }
    }
}

impl StepResults {
    pub(super) fn checksums(&self) -> Option<InputChecksums> {
        match self.resume {
            Some(Resume::Replay { hash, .. }) => Some(InputChecksums {
                hash,
                num_records: self.amt.records as u64,
            }),
            _ => None,
        }
    }
}

const MAX_TOKENLIST_LEN: usize = 1_000_000;

/// Tracks the set of tokens issued by the connector and the state needed to report
/// completion status for these tokens.
///
/// A token is the number of records ingested by the connector at the time the token
/// was requested.  When all records up to this offset are pushed from the connector's
/// queue into the circuit, we record the corresponding index (global offset) in the
/// globally ordered stream of input to the circuit.  We can then determine whether
/// all records associated with the token have been fully processed by comparing this
/// offset with the `total_completed_records` counter.
///
/// # Garbage collection
///
/// A new token is generated on each /completion_token API call, as well as for each
/// HTTP /ingress request. We therefore need to be careful to bound the number of tokens
/// in the list.
///
/// We do this by removing all except one token whose global offset is `<= total_completed_records`
/// from the list.  This bounds the number of tokens to the size of the connectors
/// input queue + the number of inputs currently in the circuit of buffered in the output
/// connectors, which can be quite large. Therefore we additionally bound the list to `MAX_TOKENLIST_LEN`
/// tokens, dropping the oldest token when queue is full. This is safe (i.e., we can never
/// report an operation that hasn't been completed as completed), but it can delay the
/// completion notification. In the worst case, if there are always `> MAX_TOKENLIST_LEN` incomplete
/// tokens in the queue, we will never issue any completion notifications, but that's
/// probably ok, as this means that no one's waiting for these tokens.
#[derive(Serialize)]
#[repr(transparent)]
struct TokenList(Mutex<TokenListInner>);

#[derive(Serialize)]
struct TokenListInner {
    /// List of (token, global offset) mappings.
    token_list: VecDeque<(u64, Option<u64>)>,
}

impl TokenListInner {
    fn new() -> Self {
        Self {
            token_list: VecDeque::new(),
        }
    }

    /// Perform queue maintenance.
    ///
    /// Invoked when a new token is pushed to the queue or when additional records are pushed from
    /// the connector to the circuit.
    ///
    /// * Set `total_circuit_input_records` as the global offset for all tokens that are <= connector_input_records,
    ///   and that don't have a global offset yet.
    /// * GC all but one tokens whose global offset is `<= total_completed_records`.
    /// * Remove old tokens that exceed queue capacity.
    ///
    /// # Arguments
    ///
    /// * `connector_circuit_input_records` - total number of inputs **from this connector** ingested by the circuit.
    /// * `total_circuit_input_records` - total number of inputs across all connectors ingested by the circuit.
    /// * `total_completed_records` - the number of records processed by the pipeline to completion.
    fn update(
        &mut self,
        connector_circuit_input_records: u64,
        total_circuit_input_records: u64,
        total_completed_records: u64,
    ) {
        // Label all unlabeled tokens <= connector_input_records with total_circuit_input_records
        let first_unlabeled = self
            .token_list
            .partition_point(|(_token, label)| label.is_some());

        for (token, label) in self.token_list.range_mut(first_unlabeled..) {
            if *token <= connector_circuit_input_records {
                *label = Some(total_circuit_input_records)
            } else {
                break;
            }
        }

        // Delete all but one tokens whose global offset is `<= total_completed_records`.
        let first_completed = self.token_list.partition_point(|(_token, label)| {
            if let Some(label) = label {
                *label <= total_completed_records
            } else {
                false
            }
        });

        if first_completed > 0 {
            for _ in 0..first_completed - 1 {
                self.token_list.pop_front();
            }
        }

        // Remove old tokens.
        while self.token_list.len() > MAX_TOKENLIST_LEN {
            self.token_list.pop_back();
        }
    }

    /// Create a token to track records ingested by the connector up to now.
    ///
    /// # Arguments
    ///
    /// * `token_input_records` - the number of records queued by the connector so far
    ///   (not all of these record have been pushed to the circuit).
    fn add_token(&mut self, token_input_records: u64) {
        // Don't track more than one token for the same offset.
        if !self.token_list.is_empty() {
            // The number of ingested records grows monotonically.
            debug_assert!(self.token_list[self.token_list.len() - 1].0 <= token_input_records);

            if self.token_list[self.token_list.len() - 1].0 >= token_input_records {
                return;
            }
        }

        self.token_list.push_back((token_input_records, None));
    }

    /// Check completion status of a token.
    ///
    /// # Arguments
    ///
    /// * `token` - offset in the connector's input stream.
    ///
    /// * `total_completed_records` - the total number of records processed by the pipeline to completion.
    ///   The token is considered completed if its associated global offset is `<= total_completed_records`.
    ///
    /// # Return value
    ///
    /// `true` if the token is completed; `false` otherwise.
    fn completion_status(&self, token: u64, total_completed_records: u64) -> bool {
        // The first queued token `>= token`.
        let nearest_index = self
            .token_list
            .partition_point(|(queue_token, _label)| *queue_token < token);

        if nearest_index >= self.token_list.len() {
            warn!("Client requested completion status of unknown token {token}");
            return false;
        };

        if let Some(global_offset) = &self.token_list[nearest_index].1 {
            *global_offset <= total_completed_records
        } else {
            false
        }
    }
}

impl TokenList {
    fn new() -> Self {
        Self(Mutex::new(TokenListInner::new()))
    }
    fn update(
        &self,
        connector_circuit_input_records: u64,
        total_circuit_input_records: u64,
        total_completed_records: u64,
    ) {
        self.0.lock().unwrap().update(
            connector_circuit_input_records,
            total_circuit_input_records,
            total_completed_records,
        );
    }

    fn add_token(&self, token_input_records: u64) {
        self.0.lock().unwrap().add_token(token_input_records);
    }

    fn completion_status(&self, token: u64, total_completed_records: u64) -> bool {
        self.0
            .lock()
            .unwrap()
            .completion_status(token, total_completed_records)
    }
}

/// Input endpoint status information.
#[derive(Serialize)]
pub struct InputEndpointStatus {
    pub endpoint_name: String,

    /// Endpoint configuration (doesn't change).
    #[serde(serialize_with = "serialize_input_endpoint_config")]
    pub config: InputEndpointConfig,

    /// Performance metrics.
    pub metrics: InputEndpointMetrics,

    /// The first fatal error that occurred at the endpoint.
    pub fatal_error: Mutex<Option<String>>,

    /// Progress within the latest step.
    #[serde(skip)]
    pub progress: Mutex<Option<StepResults>>,

    /// May be None during endpoint initialization.
    #[serde(skip)]
    pub reader: Option<Box<dyn InputReader>>,

    /// Endpoint support for fault tolerance.
    #[serde(skip)]
    pub fault_tolerance: Option<FtModel>,

    /// Endpoint has been paused by the user.
    ///
    /// When `true`, the endpoint doesn't produce any data even when the pipeline
    /// is running.
    ///
    /// This flag is set to `true` on startup if the `paused` flag in the
    /// endpoint configuration is `true`. At runtime, the value of the flag is
    /// controlled via the `/tables/<table_name>/connectors/<connector_name>/start` and
    /// `/tables/<table_name>/connectors/<connector_name>/pause` endpoints.
    pub paused: AtomicBool,

    /// Endpoint is currently a barrier checkpointing and suspend.
    ///
    /// An endpoint blocks checkpoint and suspend if its current input position
    /// is not one where it can resume.  In such a case, the user can still
    /// request checkpoint and suspend, which will be carried out as soon as the
    /// endpoint or endpoints advance beyond the barriers.
    pub barrier: AtomicBool,

    /// Completion tokens associated with the endpoint.
    #[serde(skip)]
    completion_tokens: TokenList,
}

#[derive(Serialize)]
struct StreamOnly<'a> {
    stream: &'a str,
}

/// Serialize only `config.stream`, omitting other fields.
fn serialize_input_endpoint_config<S>(
    config: &InputEndpointConfig,
    serializer: S,
) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    StreamOnly {
        stream: &config.stream,
    }
    .serialize(serializer)
}

impl InputEndpointStatus {
    pub fn new(
        endpoint_name: &str,
        config: InputEndpointConfig,
        fault_tolerance: Option<FtModel>,
        initial_statistics: Option<&CheckpointInputEndpointMetrics>,
    ) -> Self {
        let paused_by_user =
            config.connector_config.paused || config.connector_config.start_after.is_some();

        Self {
            endpoint_name: endpoint_name.to_string(),
            config,
            metrics: initial_statistics
                .map_or(InputEndpointMetrics::default(), |initial_statistics| {
                    initial_statistics.into()
                }),
            fatal_error: Mutex::new(None),
            progress: Mutex::new(None),
            paused: AtomicBool::new(paused_by_user),
            barrier: AtomicBool::new(false),
            reader: None,
            fault_tolerance,
            completion_tokens: TokenList::new(),
        }
    }

    /// Increment the number of buffered bytes and records; return
    /// the previous number of buffered records.
    fn add_buffered(&self, amt: BufferSize) -> u64 {
        // We are only updating statistics here, so no need to pay for
        // strong consistency.
        self.metrics
            .total_bytes
            .fetch_add(amt.bytes as u64, Ordering::Relaxed);
        self.metrics
            .total_records
            .fetch_add(amt.records as u64, Ordering::Relaxed);
        self.metrics
            .buffered_bytes
            .fetch_add(amt.bytes as u64, Ordering::Relaxed);
        self.metrics
            .buffered_records
            .fetch_add(amt.records as u64, Ordering::AcqRel)
    }

    fn eoi(&self) {
        debug!("endpoint {} has reached end of input", self.endpoint_name);
        self.metrics.end_of_input.store(true, Ordering::Release);
    }

    fn is_eoi(&self) -> bool {
        self.metrics.end_of_input.load(Ordering::Acquire)
    }

    /// True if the endpoint has reached end of input and doesn't have
    /// any more buffered records.
    fn finished(&self) -> bool {
        self.is_eoi() && self.metrics.buffered_records.load(Ordering::Acquire) == 0
    }

    /// Increment parser error counter.
    fn parse_error(&self) {
        self.metrics.num_parse_errors.fetch_add(1, Ordering::AcqRel);
    }

    /// Increment transport error counter.  If this is the first fatal error,
    /// save it in `self.fatal_error`.
    fn transport_error(&self, fatal: bool, error: &AnyError) {
        self.metrics
            .num_transport_errors
            .fetch_add(1, Ordering::AcqRel);
        if fatal {
            let mut fatal_error = self.fatal_error.lock().unwrap();
            if fatal_error.is_none() {
                *fatal_error = Some(error.to_string());
            }
        }
    }

    /// True if the endpoint's `paused_by_user` flag is set to `true`.
    pub fn is_paused_by_user(&self) -> bool {
        self.paused.load(Ordering::Acquire)
    }

    /// True if the number of records buffered by the endpoint exceeds
    /// its `max_queued_records` config parameter.
    pub fn is_full(&self) -> bool {
        let buffered_records = self.metrics.buffered_records.load(Ordering::Acquire);
        let max_queued_records = self.config.connector_config.max_queued_records;
        buffered_records >= max_queued_records
    }

    /// Endpoint pushed additional records to the circuit.
    ///
    /// Updates:
    /// * `buffered_records`
    /// * `circuit_input_records`
    /// * `completion_tokens`
    ///
    /// # Arguments
    ///
    /// `total_circuit_input_records`, `total_completed_records` - current pipeline-level metrics
    /// (already updated with `step_results`) used to update `completion_tokens`.
    fn extended(
        &self,
        step_results: StepResults,
        total_circuit_input_records: u64,
        total_completed_records: u64,
    ) {
        let num_records = step_results.amt.records as u64;
        let num_bytes = step_results.amt.bytes as u64;
        *self.progress.lock().unwrap() = Some(step_results);
        self.metrics
            .buffered_records
            .fetch_sub(num_records, Ordering::Relaxed);
        self.metrics
            .buffered_bytes
            .fetch_sub(num_bytes, Ordering::Relaxed);
        self.metrics
            .circuit_input_bytes
            .fetch_add(num_bytes, Ordering::Relaxed);
        let old_circuit_input_records = self
            .metrics
            .circuit_input_records
            .fetch_add(num_records, Ordering::AcqRel);
        self.completion_tokens.update(
            old_circuit_input_records + num_records,
            total_circuit_input_records,
            total_completed_records,
        );
    }

    /// Push a new completion token that corresponds to the current input offset to the
    /// `completion_tokens` queue.
    ///
    /// Returns the input offset that can be used to build `CompletionToken`.
    fn completion_token(
        &self,
        total_circuit_input_records: u64,
        total_completed_records: u64,
    ) -> u64 {
        let token_input_records = self.metrics.total_records.load(Ordering::Acquire);

        // To avoid a race, we add a token _before_ reading the circuit_input_records
        // metric and using it to update the token list.
        self.completion_tokens.add_token(token_input_records);

        let connector_circuit_input_records =
            self.metrics.circuit_input_records.load(Ordering::Acquire);

        self.completion_tokens.update(
            connector_circuit_input_records,
            total_circuit_input_records,
            total_completed_records,
        );
        token_input_records
    }

    /// Check the status of a completion token.
    fn completion_status(&self, token: u64, total_completed_records: u64) -> bool {
        self.completion_tokens
            .completion_status(token, total_completed_records)
    }

    pub fn is_barrier(&self) -> bool {
        self.barrier.load(Ordering::Relaxed)
    }

    pub fn set_barrier(&self, barrier: bool) {
        self.barrier.store(barrier, Ordering::Relaxed);
    }
}

#[derive(Default, Serialize)]
pub struct OutputEndpointMetrics {
    /// Records and bytes sent on the underlying transport (HTTP, Kafka, etc.)
    /// to the endpoint.
    ///
    /// These only increase.
    pub transmitted_records: AtomicU64,
    pub transmitted_bytes: AtomicU64,

    /// Number of queued records.
    ///
    /// These are the records sent by the main circuit thread to the endpoint thread.
    /// Upon dequeuing the record, it gets buffered or sent directly to the output
    /// transport.
    ///
    /// These increase as records are queued.  They decrease as records are
    /// dequeued either to be buffered (see
    /// [buffered_records](Self::buffered_records)) or to be output directly.
    pub queued_records: AtomicU64,
    pub queued_batches: AtomicU64,

    /// Number of records pushed to the output buffer.
    ///
    /// Note that this may not be equal to the current size of the
    /// buffer, since the buffer consolidates records, e.g., inserts
    /// and deletes can cancel out over time.
    ///
    /// These increase as records are moved from queues to buffers.  They then
    /// fall abruptly to 0 because the buffers are always flushed as a whole.
    pub buffered_records: AtomicU64,
    pub buffered_batches: AtomicU64,

    pub num_encode_errors: AtomicU64,
    pub num_transport_errors: AtomicU64,

    /// The number of input records processed by the circuit.
    ///
    /// This metric tracks the end-to-end progress of the pipeline: the output
    /// of this endpoint is equal to the output of the circuit after
    /// processing `total_processed_input_records` records.
    pub total_processed_input_records: AtomicU64,
}

impl OutputEndpointMetrics {
    fn new(
        total_processed_input_records: u64,
        initial_statistics: Option<&CheckpointOutputEndpointMetrics>,
    ) -> Self {
        let initial_statistics = initial_statistics.cloned().unwrap_or_default();
        Self {
            transmitted_records: AtomicU64::new(initial_statistics.transmitted_records),
            transmitted_bytes: AtomicU64::new(initial_statistics.transmitted_bytes),
            queued_records: AtomicU64::new(0),
            queued_batches: AtomicU64::new(0),
            buffered_records: AtomicU64::new(0),
            buffered_batches: AtomicU64::new(0),
            num_encode_errors: AtomicU64::new(initial_statistics.num_encode_errors),
            num_transport_errors: AtomicU64::new(initial_statistics.num_transport_errors),
            total_processed_input_records: AtomicU64::new(total_processed_input_records),
        }
    }
}

/// Output endpoint status information.
#[derive(Serialize)]
pub struct OutputEndpointStatus {
    pub endpoint_name: String,

    /// Endpoint configuration (doesn't change).
    #[serde(serialize_with = "serialize_output_endpoint_config")]
    pub config: OutputEndpointConfig,

    /// Performance metrics.
    pub metrics: OutputEndpointMetrics,

    /// The first fatal error that occurred at the endpoint.
    pub fatal_error: Mutex<Option<String>>,
}

impl OutputEndpointStatus {
    pub fn is_busy(&self) -> bool {
        self.metrics.buffered_records.load(Ordering::Relaxed) != 0
            || self.metrics.queued_records.load(Ordering::Relaxed) != 0
    }
}

/// Serialize only `config.stream`, omitting other fields.
fn serialize_output_endpoint_config<S>(
    config: &OutputEndpointConfig,
    serializer: S,
) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    StreamOnly {
        stream: &config.stream,
    }
    .serialize(serializer)
}

/// Public read API.
impl OutputEndpointStatus {
    pub fn transmitted_records(&self) -> u64 {
        self.metrics.transmitted_records.load(Ordering::Acquire)
    }
}

impl OutputEndpointStatus {
    fn new(
        endpoint_name: &str,
        config: &OutputEndpointConfig,
        total_processed_records: u64,
        initial_statistics: Option<&CheckpointOutputEndpointMetrics>,
    ) -> Self {
        Self {
            endpoint_name: endpoint_name.to_string(),
            config: config.clone(),
            metrics: OutputEndpointMetrics::new(total_processed_records, initial_statistics),
            fatal_error: Mutex::new(None),
        }
    }

    fn enqueue_batch(&self, num_records: usize) {
        self.metrics
            .queued_records
            .fetch_add(num_records as u64, Ordering::AcqRel);
        self.metrics.queued_batches.fetch_add(1, Ordering::AcqRel);
    }

    /// A batch has been pushed to the output transport directly from the queue,
    /// bypassing the buffer.
    fn output_batch(&self, total_processed_input_records: Option<u64>, num_records: usize) -> u64 {
        if let Some(total_processed_input_records) = total_processed_input_records {
            self.metrics
                .total_processed_input_records
                .store(total_processed_input_records, Ordering::Release);
        }

        let old = self
            .metrics
            .queued_records
            .fetch_sub(num_records as u64, Ordering::AcqRel);
        self.metrics.queued_batches.fetch_sub(1, Ordering::AcqRel);

        old
    }

    /// A batch has been read from a queue into the buffer.
    fn buffer_batch(&self, num_records: usize) -> u64 {
        self.metrics
            .buffered_records
            .fetch_add(num_records as u64, Ordering::AcqRel);
        // Don't count empty batches.
        if num_records > 0 {
            self.metrics.buffered_batches.fetch_add(1, Ordering::AcqRel);
        }

        let old = self
            .metrics
            .queued_records
            .fetch_sub(num_records as u64, Ordering::AcqRel);
        self.metrics.queued_batches.fetch_sub(1, Ordering::AcqRel);

        old
    }

    /// The content of the buffer has been pushed to the transport endpoint.
    ///
    /// # Arguments
    ///
    /// * `total_processed_input_records` - the output of the endpoint is now
    ///   up to speed with the output of the circuit after processing this
    ///   many records.
    fn output_buffered_batches(&self, total_processed_input_records: u64) {
        self.metrics.buffered_records.store(0, Ordering::Release);
        self.metrics.buffered_batches.store(0, Ordering::Release);

        self.metrics
            .total_processed_input_records
            .store(total_processed_input_records, Ordering::Release);
    }

    fn output_buffer(&self, num_bytes: usize, num_records: usize) {
        self.metrics
            .transmitted_bytes
            .fetch_add(num_bytes as u64, Ordering::Relaxed);
        self.metrics
            .transmitted_records
            .fetch_add(num_records as u64, Ordering::Relaxed);
    }

    /// Increment encoder error counter.
    fn encode_error(&self) {
        self.metrics
            .num_encode_errors
            .fetch_add(1, Ordering::AcqRel);
    }

    /// Increment error counter.  If this is the first fatal error,
    /// save it in `self.fatal_error`.
    fn transport_error(&self, fatal: bool, error: &AnyError) {
        self.metrics
            .num_transport_errors
            .fetch_add(1, Ordering::AcqRel);
        if fatal {
            let mut fatal_error = self.fatal_error.lock().unwrap();
            if fatal_error.is_none() {
                *fatal_error = Some(error.to_string());
            }
        }
    }

    fn num_total_processed_input_records(&self) -> u64 {
        self.metrics
            .total_processed_input_records
            .load(Ordering::Acquire)
    }
}
