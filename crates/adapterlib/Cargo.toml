[package]
name = "feldera-adapterlib"

edition = { workspace = true }
version = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
readme = { workspace = true }

[features]
with-avro = ["apache-avro"]

[dependencies]
feldera-types = { workspace = true }
dbsp = { workspace = true }
anyhow = { workspace = true, features = ["backtrace"] }
serde_yaml = { workspace = true }
actix-web = { workspace = true, features = ["cookies", "macros", "compress-gzip", "compress-brotli"] }
erased-serde = { workspace = true }
dyn-clone = { workspace = true }
arrow = { workspace = true, features = ["chrono-tz"] }
apache-avro = { workspace = true, optional = true }
serde_arrow = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true, features = ["raw_value"] }
rmp-serde = { workspace = true }
rmpv = { workspace = true, features = ["with-serde"] }
bytemuck = { workspace = true }
num-traits = { workspace = true }
num-derive = { workspace = true }
tokio = { workspace = true, features = ["sync"] }
xxhash-rust = { workspace = true, features = ["xxh3"] }
datafusion = { workspace = true }
thiserror = { workspace = true }
serde_json_path_to_error = { workspace = true }

[package.metadata.cargo-machete]
ignored = ["num-traits"]
