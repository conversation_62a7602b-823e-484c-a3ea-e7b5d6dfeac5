[package]
name = "feldera-datagen"
edition = { workspace = true }
version = { workspace = true }
homepage = { workspace = true }
repository = { workspace = true }
license = { workspace = true }
authors = { workspace = true }
rust-version = { workspace = true }
readme = { workspace = true }

[dependencies]
feldera-types = { workspace = true }
feldera-adapterlib = { workspace = true }
dbsp = { workspace = true }
anyhow = { workspace = true }
chrono = { workspace = true }
feldera-fxp = { workspace = true }
num-traits = { workspace = true }
rand = { workspace = true, features = ["small_rng"] }
rand_distr = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
governor = { workspace = true }
fake = { workspace = true, features = ["http"] }
rmpv = { workspace = true, features = ["with-serde"] }
serde = { workspace = true, features = ["derive"] }
async-channel = { workspace = true }
range-set = { workspace = true }
xxhash-rust = { workspace = true, features = ["xxh3"] }
uuid = { workspace = true, features = ["v4", "std"] }
crossbeam = { workspace = true }