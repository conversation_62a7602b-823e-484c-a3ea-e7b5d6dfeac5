# Seeds for failure cases proptest has generated in the past. It is
# automatically read and these particular cases re-run before any
# novel cases are generated.
#
# It is recommended to check this file in to source control so that
# everyone who runs the test benefits from these saved cases.
cc 9fc41c07e13c1178960b37c62948b54cbf17883438f50ee4be872fe750cdd256 # shrinks to trace = [[(2, ((0, 100), 1)), (2, ((1, 100), 1))]]
cc e4eef1456c6c1809431c83d2ccb42061e53acf8782ed8599d42cf9965998af9d # shrinks to trace = [[(3, ((0, 100), 1)), (3, ((1, 100), 1))]]
cc cf1b0effb1bdee10cb650bbde0aa44d70a73faa229600703a6c62f50df071938 # shrinks to trace = [[(0, ((1, 100), 1))], [(0, ((0, 100), 1))]]
cc 15b9defc4a6313a222f0b1fc2a34e9eb60b73462ab975f2964dfd4e8fa4cacbc # shrinks to trace = [[(0, ((0, 100), 1))]]
cc d0ccb905085a1259226d308238c2b574b023e0b345277f223f584973938a83ba # shrinks to trace = [[(0, ((0, 100), 1))]]
cc 24b08ac3f9857ca13f9cebf0e86a11c2393403ee04c2ba48edfdf7d26793a49b # shrinks to trace = [[(0, ((0, 100), 1))], [(1, ((5551, 100), 1)), (2, ((3064, 100), 1)), (2, ((9413, 100), 1)), (3, ((9220, 100), 1))], [(3, ((10398, 100), 1)), (2, ((6592, 100), 1)), (4, ((13617, 100), 1)), (0, ((13359, 100), 1)), (0, ((6827, 100), 1)), (3, ((8264, 100), 1)), (1, ((7131, 100), 1)), (2, ((5433, 100), 1)), (0, ((5132, 100), 1)), (0, ((12284, 100), 1)), (0, ((7320, 100), 1)), (1, ((11710, 100), 1)), (2, ((13117, 100), 1))], [(1, ((6590, 100), 1)), (1, ((12734, 100), 1)), (2, ((14313, 100), 1)), (1, ((11419, 100), 1)), (1, ((14983, 100), 1)), (4, ((9316, 100), 1)), (4, ((10589, 100), 1)), (3, ((14944, 100), 1))], [], [(2, ((19584, 100), 1)), (4, ((15696, 100), 1)), (3, ((18418, 100), 1)), (0, ((10941, 100), 1))], [(1, ((15743, 100), 1)), (4, ((12455, 100), 1)), (1, ((18706, 100), 1)), (2, ((15644, 100), 1)), (1, ((16544, 100), 1)), (3, ((20442, 100), 1)), (0, ((17929, 100), 1)), (0, ((20690, 100), 1)), (1, ((20324, 100), 1)), (3, ((18655, 100), 1)), (3, ((16953, 100), 1)), (1, ((16982, 100), 1)), (3, ((14199, 100), 1)), (0, ((13526, 100), 1)), (1, ((17300, 100), 1)), (1, ((15216, 100), 1))], [(1, ((16175, 100), 1)), (4, ((23327, 100), 1)), (4, ((15917, 100), 1))], [(4, ((23444, 100), 1)), (4, ((18720, 100), 1)), (2, ((24406, 100), 1)), (3, ((17374, 100), 1))], [(3, ((19741, 100), 1)), (1, ((19785, 100), 1)), (2, ((19469, 100), 1)), (4, ((19884, 100), 1)), (4, ((22156, 100), 1))], [(1, ((28827, 100), 1)), (1, ((20197, 100), 1)), (1, ((28301, 100), 1)), (0, ((20154, 100), 1)), (3, ((27619, 100), 1)), (1, ((22454, 100), 1)), (4, ((25229, 100), 1)), (1, ((26930, 100), 1)), (2, ((26892, 100), 1)), (1, ((21768, 100), 1)), (0, ((28981, 100), 1)), (1, ((28159, 100), 1)), (3, ((21766, 100), 1)), (1, ((20866, 100), 1)), (3, ((22804, 100), 1)), (0, ((29938, 100), 1)), (0, ((22231, 100), 1)), (2, ((22318, 100), 1)), (1, ((21216, 100), 1))], [(0, ((28006, 100), 1)), (0, ((24541, 100), 1)), (4, ((25064, 100), 1)), (0, ((27602, 100), 1)), (1, ((29940, 100), 1)), (2, ((29923, 100), 1))], [(1, ((33303, 100), 1)), (2, ((24722, 100), 1)), (1, ((30368, 100), 1)), (1, ((24051, 100), 1)), (0, ((25879, 100), 1)), (4, ((31283, 100), 1)), (1, ((33916, 100), 1)), (0, ((24965, 100), 1)), (4, ((28619, 100), 1)), (0, ((28839, 100), 1)), (2, ((30209, 100), 1)), (0, ((33338, 100), 1)), (1, ((24510, 100), 1))], [(4, ((34563, 100), 1)), (4, ((29488, 100), 1))], [(2, ((33930, 100), 1)), (3, ((28645, 100), 1))], [(4, ((33207, 100), 1)), (3, ((35892, 100), 1)), (0, ((37746, 100), 1)), (0, ((34412, 100), 1)), (0, ((31963, 100), 1)), (1, ((38424, 100), 1)), (0, ((33738, 100), 1)), (2, ((33656, 100), 1)), (2, ((35148, 100), 1))], [(3, ((36406, 100), 1)), (2, ((33756, 100), 1)), (0, ((32734, 100), 1)), (1, ((39115, 100), 1))], [(2, ((35007, 100), 1)), (4, ((36111, 100), 1)), (4, ((41265, 100), 1)), (3, ((39712, 100), 1)), (4, ((36981, 100), 1)), (0, ((34534, 100), 1)), (4, ((34309, 100), 1)), (2, ((39633, 100), 1)), (2, ((37525, 100), 1)), (1, ((41773, 100), 1)), (1, ((38631, 100), 1)), (1, ((41404, 100), 1)), (3, ((41203, 100), 1)), (3, ((40321, 100), 1)), (1, ((36219, 100), 1)), (0, ((37592, 100), 1)), (2, ((42409, 100), 1)), (1, ((42135, 100), 1))], [], [(0, ((39208, 100), 1)), (1, ((39572, 100), 1)), (4, ((40367, 100), 1)), (3, ((40555, 100), 1)), (2, ((39028, 100), 1)), (0, ((43686, 100), 1)), (3, ((40821, 100), 1)), (2, ((42767, 100), 1)), (3, ((44240, 100), 1))], [], [(4, ((45914, 100), 1)), (2, ((50694, 100), 1)), (3, ((50860, 100), 1)), (1, ((43585, 100), 1)), (1, ((43353, 100), 1)), (4, ((48997, 100), 1))], [(2, ((49942, 100), 1)), (4, ((52250, 100), 1)), (0, ((46476, 100), 1)), (3, ((49708, 100), 1)), (3, ((51911, 100), 1)), (3, ((51797, 100), 1)), (2, ((46555, 100), 1)), (2, ((45967, 100), 1)), (0, ((49207, 100), 1)), (2, ((44771, 100), 1)), (4, ((52661, 100), 1)), (1, ((47109, 100), 1)), (3, ((49258, 100), 1)), (2, ((44319, 100), 1)), (1, ((52869, 100), 1))], [(3, ((49661, 100), 1)), (4, ((49773, 100), 1)), (2, ((50896, 100), 1)), (2, ((49680, 100), 1)), (1, ((50293, 100), 1)), (3, ((55340, 100), 1)), (4, ((54582, 100), 1)), (4, ((54727, 100), 1))], [(0, ((49324, 100), 1)), (4, ((55661, 100), 1)), (2, ((57142, 100), 1)), (0, ((48181, 100), 1)), (1, ((49774, 100), 1)), (4, ((54583, 100), 1)), (4, ((50921, 100), 1)), (3, ((53370, 100), 1)), (3, ((49027, 100), 1)), (1, ((56257, 100), 1)), (0, ((53610, 100), 1)), (3, ((56602, 100), 1)), (2, ((54788, 100), 1)), (0, ((53841, 100), 1)), (4, ((51096, 100), 1)), (2, ((54435, 100), 1)), (2, ((54940, 100), 1)), (3, ((49571, 100), 1))], [(4, ((57329, 100), 1)), (1, ((55760, 100), 1)), (1, ((57233, 100), 1)), (2, ((58613, 100), 1)), (0, ((50410, 100), 1)), (1, ((52273, 100), 1)), (1, ((59017, 100), 1)), (2, ((52007, 100), 1)), (3, ((56128, 100), 1)), (2, ((57514, 100), 1)), (1, ((50521, 100), 1)), (0, ((58676, 100), 1)), (2, ((57695, 100), 1)), (0, ((55647, 100), 1))], [(0, ((52577, 100), 1)), (0, ((56909, 100), 1)), (3, ((55374, 100), 1)), (0, ((61221, 100), 1)), (3, ((61198, 100), 1)), (2, ((54807, 100), 1)), (0, ((57063, 100), 1)), (1, ((60061, 100), 1)), (4, ((58007, 100), 1)), (4, ((58222, 100), 1)), (4, ((55829, 100), 1)), (3, ((54520, 100), 1)), (3, ((60135, 100), 1))], [(4, ((60513, 100), 1)), (2, ((58391, 100), 1)), (0, ((62668, 100), 1)), (4, ((63432, 100), 1)), (2, ((63947, 100), 1)), (2, ((55198, 100), 1)), (3, ((63677, 100), 1))], [(4, ((61602, 100), 1)), (2, ((59797, 100), 1)), (0, ((56784, 100), 1)), (2, ((60282, 100), 1)), (1, ((64637, 100), 1)), (2, ((57278, 100), 1))], [(1, ((62238, 100), 1))], [(2, ((61131, 100), 1)), (2, ((63069, 100), 1)), (4, ((63986, 100), 1)), (3, ((68187, 100), 1)), (4, ((63328, 100), 1)), (1, ((62477, 100), 1)), (3, ((60930, 100), 1))], [(3, ((63903, 100), 1)), (3, ((65053, 100), 1)), (0, ((64391, 100), 1)), (1, ((67899, 100), 1)), (2, ((68526, 100), 1)), (2, ((65746, 100), 1)), (2, ((66909, 100), 1)), (0, ((68423, 100), 1)), (2, ((62109, 100), 1)), (4, ((63832, 100), 1)), (4, ((69676, 100), 1)), (2, ((66187, 100), 1)), (4, ((68316, 100), 1)), (2, ((69292, 100), 1))], [(0, ((68454, 100), 1)), (1, ((66245, 100), 1)), (3, ((71189, 100), 1)), (4, ((65421, 100), 1)), (4, ((73704, 100), 1)), (0, ((71318, 100), 1)), (1, ((68225, 100), 1)), (4, ((69479, 100), 1)), (0, ((66429, 100), 1)), (0, ((66837, 100), 1)), (3, ((66747, 100), 1))], [(2, ((74463, 100), 1)), (2, ((68754, 100), 1)), (3, ((75208, 100), 1)), (1, ((70099, 100), 1)), (4, ((69125, 100), 1)), (2, ((66132, 100), 1)), (0, ((68908, 100), 1)), (3, ((71062, 100), 1)), (0, ((66313, 100), 1)), (2, ((75535, 100), 1)), (0, ((70029, 100), 1)), (2, ((71192, 100), 1)), (4, ((74540, 100), 1)), (2, ((68748, 100), 1)), (1, ((70117, 100), 1)), (2, ((73409, 100), 1)), (1, ((71736, 100), 1)), (2, ((75072, 100), 1)), (2, ((68583, 100), 1))], [(0, ((72057, 100), 1)), (2, ((72369, 100), 1)), (0, ((70123, 100), 1))], [(0, ((76040, 100), 1)), (4, ((79054, 100), 1)), (4, ((74849, 100), 1)), (4, ((74932, 100), 1)), (3, ((76598, 100), 1)), (3, ((72735, 100), 1)), (2, ((75821, 100), 1)), (3, ((73473, 100), 1)), (2, ((73596, 100), 1)), (3, ((75910, 100), 1)), (0, ((79219, 100), 1)), (3, ((70647, 100), 1)), (2, ((71083, 100), 1)), (2, ((79195, 100), 1)), (1, ((73304, 100), 1)), (2, ((79009, 100), 1)), (4, ((76563, 100), 1))], [(3, ((75493, 100), 1)), (0, ((75857, 100), 1)), (3, ((77407, 100), 1)), (2, ((73847, 100), 1)), (4, ((80893, 100), 1)), (0, ((78534, 100), 1)), (2, ((75096, 100), 1)), (2, ((72255, 100), 1)), (4, ((81652, 100), 1)), (3, ((75843, 100), 1)), (3, ((72268, 100), 1)), (0, ((79081, 100), 1)), (2, ((78564, 100), 1)), (2, ((81113, 100), 1)), (0, ((79258, 100), 1)), (1, ((76026, 100), 1)), (3, ((78585, 100), 1)), (1, ((74425, 100), 1))], [(0, ((80734, 100), 1)), (0, ((75590, 100), 1)), (2, ((76630, 100), 1)), (3, ((75695, 100), 1)), (2, ((76141, 100), 1)), (3, ((77465, 100), 1)), (4, ((78395, 100), 1)), (1, ((74512, 100), 1)), (1, ((78632, 100), 1)), (4, ((81256, 100), 1)), (2, ((77075, 100), 1)), (1, ((76246, 100), 1)), (2, ((75359, 100), 1)), (0, ((83025, 100), 1)), (0, ((78138, 100), 1)), (4, ((74524, 100), 1)), (0, ((82286, 100), 1)), (0, ((78629, 100), 1)), (0, ((83509, 100), 1))], [(2, ((80702, 100), 1)), (3, ((81244, 100), 1)), (2, ((78593, 100), 1)), (1, ((78162, 100), 1)), (1, ((79833, 100), 1)), (4, ((82944, 100), 1)), (1, ((81855, 100), 1)), (1, ((80369, 100), 1)), (2, ((76886, 100), 1)), (2, ((76636, 100), 1)), (4, ((78735, 100), 1)), (4, ((81615, 100), 1)), (1, ((77167, 100), 1))], [(1, ((85772, 100), 1)), (3, ((84125, 100), 1)), (1, ((78038, 100), 1)), (3, ((84628, 100), 1))], [(0, ((84274, 100), 1)), (2, ((89136, 100), 1)), (3, ((89825, 100), 1)), (4, ((82572, 100), 1)), (3, ((80457, 100), 1)), (1, ((84644, 100), 1))], [(0, ((86312, 100), 1)), (0, ((90887, 100), 1)), (2, ((85368, 100), 1)), (1, ((90069, 100), 1)), (2, ((84963, 100), 1)), (0, ((83523, 100), 1)), (0, ((82514, 100), 1)), (2, ((82865, 100), 1)), (0, ((85931, 100), 1)), (0, ((88649, 100), 1))], [], [(3, ((94481, 100), 1)), (3, ((94998, 100), 1)), (1, ((95383, 100), 1)), (2, ((86535, 100), 1)), (2, ((89514, 100), 1)), (4, ((95729, 100), 1)), (4, ((86066, 100), 1)), (0, ((94031, 100), 1))], [(4, ((92481, 100), 1)), (4, ((88678, 100), 1))], [(3, ((97448, 100), 1)), (3, ((93128, 100), 1)), (3, ((97119, 100), 1)), (0, ((95691, 100), 1)), (1, ((99954, 100), 1))], [(0, ((92247, 100), 1)), (2, ((94182, 100), 1)), (3, ((100395, 100), 1)), (0, ((99314, 100), 1)), (1, ((93466, 100), 1)), (0, ((100582, 100), 1)), (0, ((94766, 100), 1))], [(0, ((95224, 100), 1)), (0, ((94159, 100), 1)), (0, ((98532, 100), 1)), (2, ((102038, 100), 1)), (0, ((100165, 100), 1)), (1, ((94780, 100), 1)), (2, ((100246, 100), 1))], [(2, ((100178, 100), 1)), (4, ((101646, 100), 1))], [(3, ((105455, 100), 1)), (3, ((99318, 100), 1)), (4, ((101658, 100), 1)), (4, ((105987, 100), 1)), (2, ((99805, 100), 1)), (4, ((99088, 100), 1)), (4, ((106241, 100), 1)), (2, ((106568, 100), 1)), (3, ((99368, 100), 1)), (3, ((98222, 100), 1)), (4, ((106491, 100), 1)), (1, ((101473, 100), 1)), (4, ((103368, 100), 1)), (2, ((101602, 100), 1)), (4, ((106856, 100), 1)), (4, ((101284, 100), 1)), (4, ((103372, 100), 1)), (3, ((106518, 100), 1))], [(4, ((108965, 100), 1)), (0, ((101387, 100), 1)), (0, ((101136, 100), 1)), (3, ((106584, 100), 1)), (3, ((108477, 100), 1))], [], [(2, ((104432, 100), 1)), (4, ((109040, 100), 1)), (3, ((111208, 100), 1)), (4, ((105477, 100), 1)), (0, ((110665, 100), 1)), (1, ((104716, 100), 1)), (1, ((110720, 100), 1)), (4, ((107446, 100), 1)), (0, ((113686, 100), 1)), (0, ((107742, 100), 1)), (3, ((111574, 100), 1)), (4, ((109006, 100), 1)), (0, ((111996, 100), 1)), (0, ((105871, 100), 1))], [(2, ((115298, 100), 1)), (1, ((111084, 100), 1)), (2, ((111655, 100), 1)), (2, ((107762, 100), 1)), (0, ((108128, 100), 1)), (3, ((109759, 100), 1))], [(4, ((113866, 100), 1)), (1, ((112428, 100), 1)), (2, ((108446, 100), 1)), (2, ((112121, 100), 1)), (0, ((110477, 100), 1)), (4, ((113425, 100), 1)), (1, ((117182, 100), 1)), (3, ((111248, 100), 1)), (4, ((117304, 100), 1)), (0, ((112539, 100), 1)), (1, ((112966, 100), 1)), (1, ((113065, 100), 1))], [(4, ((110729, 100), 1)), (4, ((116005, 100), 1)), (2, ((111554, 100), 1)), (2, ((112565, 100), 1)), (3, ((114691, 100), 1)), (1, ((113046, 100), 1)), (4, ((113586, 100), 1)), (2, ((115662, 100), 1)), (4, ((113902, 100), 1)), (0, ((119513, 100), 1)), (3, ((116491, 100), 1)), (2, ((118535, 100), 1)), (4, ((117404, 100), 1)), (2, ((118083, 100), 1)), (3, ((115782, 100), 1))], [(0, ((114141, 100), 1)), (0, ((120525, 100), 1)), (1, ((115112, 100), 1)), (1, ((120590, 100), 1)), (3, ((114921, 100), 1)), (2, ((119346, 100), 1)), (3, ((116465, 100), 1)), (4, ((114219, 100), 1)), (2, ((114062, 100), 1)), (2, ((113874, 100), 1)), (1, ((113415, 100), 1)), (1, ((121884, 100), 1)), (3, ((118846, 100), 1))], [(4, ((122390, 100), 1)), (3, ((116735, 100), 1)), (1, ((118383, 100), 1))], [], [(2, ((120804, 100), 1)), (3, ((122251, 100), 1)), (3, ((125019, 100), 1)), (2, ((118383, 100), 1)), (2, ((125377, 100), 1)), (3, ((127683, 100), 1)), (1, ((126667, 100), 1)), (2, ((122815, 100), 1)), (3, ((119872, 100), 1)), (2, ((122221, 100), 1)), (1, ((122771, 100), 1)), (3, ((125599, 100), 1))], [(0, ((123884, 100), 1))], [(1, ((129115, 100), 1)), (1, ((127308, 100), 1)), (0, ((123444, 100), 1)), (3, ((130776, 100), 1)), (1, ((124944, 100), 1)), (3, ((128837, 100), 1)), (2, ((131591, 100), 1)), (2, ((128363, 100), 1)), (4, ((125980, 100), 1))], [(1, ((131848, 100), 1)), (2, ((130002, 100), 1)), (0, ((133065, 100), 1)), (2, ((126526, 100), 1)), (1, ((124334, 100), 1)), (1, ((131526, 100), 1)), (4, ((125696, 100), 1)), (1, ((131734, 100), 1)), (1, ((129185, 100), 1)), (0, ((132714, 100), 1)), (4, ((131032, 100), 1)), (0, ((130047, 100), 1)), (3, ((124031, 100), 1)), (3, ((129071, 100), 1)), (2, ((127238, 100), 1))], [(1, ((129943, 100), 1)), (3, ((134558, 100), 1)), (1, ((135961, 100), 1)), (3, ((132061, 100), 1)), (4, ((128711, 100), 1)), (4, ((131771, 100), 1)), (1, ((133248, 100), 1)), (4, ((132355, 100), 1)), (2, ((133047, 100), 1)), (0, ((130382, 100), 1)), (0, ((134813, 100), 1)), (0, ((134943, 100), 1)), (2, ((135501, 100), 1)), (0, ((129979, 100), 1)), (0, ((135976, 100), 1)), (3, ((130339, 100), 1)), (2, ((127321, 100), 1)), (0, ((131878, 100), 1))], [(2, ((133644, 100), 1)), (0, ((132033, 100), 1)), (0, ((132993, 100), 1)), (1, ((132432, 100), 1)), (4, ((135011, 100), 1)), (1, ((137577, 100), 1)), (0, ((128828, 100), 1)), (1, ((137758, 100), 1)), (4, ((129571, 100), 1)), (0, ((131128, 100), 1)), (0, ((137302, 100), 1))], [(1, ((130640, 100), 1)), (0, ((136353, 100), 1)), (2, ((137615, 100), 1))], [(0, ((138151, 100), 1)), (1, ((136406, 100), 1)), (3, ((137906, 100), 1)), (4, ((137349, 100), 1)), (0, ((140080, 100), 1)), (2, ((140591, 100), 1)), (1, ((140465, 100), 1))], [(4, ((138306, 100), 1)), (4, ((142013, 100), 1)), (1, ((134966, 100), 1)), (0, ((137757, 100), 1)), (4, ((136010, 100), 1)), (3, ((134236, 100), 1)), (3, ((139868, 100), 1)), (4, ((137066, 100), 1)), (3, ((137409, 100), 1)), (2, ((135575, 100), 1)), (1, ((137870, 100), 1)), (4, ((136483, 100), 1)), (1, ((138316, 100), 1)), (3, ((138543, 100), 1)), (0, ((143074, 100), 1))], [(0, ((145947, 100), 1)), (4, ((139751, 100), 1)), (1, ((139159, 100), 1)), (1, ((143989, 100), 1)), (2, ((139159, 100), 1)), (4, ((140397, 100), 1)), (1, ((141909, 100), 1)), (3, ((136481, 100), 1)), (3, ((139718, 100), 1)), (0, ((139691, 100), 1)), (4, ((141589, 100), 1)), (2, ((145198, 100), 1)), (3, ((143698, 100), 1)), (2, ((142583, 100), 1)), (3, ((143249, 100), 1)), (4, ((140351, 100), 1)), (2, ((142121, 100), 1)), (0, ((136099, 100), 1))], [(0, ((143150, 100), 1)), (4, ((139980, 100), 1)), (3, ((147472, 100), 1)), (3, ((140564, 100), 1)), (2, ((141076, 100), 1)), (4, ((141070, 100), 1)), (2, ((145092, 100), 1)), (4, ((138835, 100), 1)), (1, ((144596, 100), 1)), (2, ((140309, 100), 1)), (4, ((142359, 100), 1)), (1, ((141320, 100), 1)), (0, ((144272, 100), 1)), (4, ((138485, 100), 1)), (4, ((143966, 100), 1)), (3, ((143051, 100), 1)), (4, ((147862, 100), 1)), (3, ((145929, 100), 1)), (1, ((142751, 100), 1))], [(4, ((148656, 100), 1)), (2, ((140775, 100), 1))], [(2, ((150073, 100), 1)), (2, ((148101, 100), 1)), (0, ((147192, 100), 1)), (1, ((142455, 100), 1)), (1, ((151032, 100), 1)), (1, ((150223, 100), 1)), (4, ((143501, 100), 1)), (4, ((148858, 100), 1)), (4, ((150109, 100), 1)), (0, ((143223, 100), 1)), (0, ((147875, 100), 1)), (0, ((142803, 100), 1)), (4, ((143893, 100), 1)), (4, ((151912, 100), 1)), (0, ((144371, 100), 1)), (1, ((146669, 100), 1)), (0, ((142264, 100), 1))], [(4, ((149255, 100), 1)), (1, ((152405, 100), 1)), (3, ((151567, 100), 1)), (4, ((151674, 100), 1)), (3, ((153167, 100), 1)), (4, ((151242, 100), 1)), (0, ((151608, 100), 1))], [(3, ((154619, 100), 1)), (1, ((148880, 100), 1)), (0, ((147659, 100), 1)), (1, ((146649, 100), 1))], [(1, ((150467, 100), 1)), (0, ((152204, 100), 1)), (0, ((153289, 100), 1)), (2, ((155295, 100), 1)), (0, ((149554, 100), 1)), (0, ((149325, 100), 1)), (0, ((156297, 100), 1)), (3, ((153743, 100), 1)), (0, ((150069, 100), 1)), (0, ((153180, 100), 1)), (3, ((153649, 100), 1)), (2, ((156792, 100), 1)), (1, ((150807, 100), 1)), (1, ((153744, 100), 1)), (0, ((149494, 100), 1))], [(3, ((153088, 100), 1)), (0, ((150644, 100), 1)), (1, ((159882, 100), 1)), (4, ((153712, 100), 1))], [(2, ((153234, 100), 1)), (4, ((158594, 100), 1)), (1, ((156345, 100), 1)), (2, ((155942, 100), 1)), (2, ((160399, 100), 1)), (4, ((158706, 100), 1)), (2, ((154989, 100), 1)), (4, ((157147, 100), 1)), (2, ((155983, 100), 1)), (3, ((154632, 100), 1)), (2, ((158491, 100), 1)), (4, ((160095, 100), 1)), (4, ((152626, 100), 1)), (1, ((157583, 100), 1))], [(2, ((159449, 100), 1)), (3, ((159884, 100), 1)), (2, ((155589, 100), 1)), (2, ((158169, 100), 1)), (1, ((161210, 100), 1)), (4, ((157189, 100), 1)), (3, ((157942, 100), 1)), (4, ((156032, 100), 1)), (1, ((163935, 100), 1)), (2, ((162558, 100), 1)), (3, ((159240, 100), 1)), (0, ((161434, 100), 1)), (3, ((163614, 100), 1)), (3, ((162839, 100), 1)), (0, ((155514, 100), 1)), (4, ((160034, 100), 1)), (0, ((159807, 100), 1))], [(0, ((158096, 100), 1)), (3, ((158444, 100), 1)), (2, ((165992, 100), 1)), (0, ((157846, 100), 1)), (2, ((162892, 100), 1)), (1, ((158289, 100), 1)), (2, ((164337, 100), 1)), (4, ((165248, 100), 1)), (2, ((163461, 100), 1)), (3, ((157591, 100), 1)), (4, ((156273, 100), 1)), (1, ((160228, 100), 1)), (4, ((161409, 100), 1))], [(4, ((162496, 100), 1)), (1, ((158268, 100), 1)), (2, ((158955, 100), 1)), (2, ((166547, 100), 1)), (3, ((167353, 100), 1)), (1, ((165062, 100), 1)), (3, ((161936, 100), 1)), (4, ((162604, 100), 1)), (4, ((158274, 100), 1)), (2, ((161921, 100), 1)), (3, ((163818, 100), 1)), (3, ((167794, 100), 1))], [(3, ((162954, 100), 1)), (3, ((161170, 100), 1)), (3, ((166933, 100), 1)), (3, ((169572, 100), 1)), (0, ((160044, 100), 1)), (2, ((167094, 100), 1)), (0, ((164457, 100), 1)), (0, ((165593, 100), 1)), (1, ((160452, 100), 1)), (2, ((162925, 100), 1)), (0, ((169223, 100), 1)), (2, ((168979, 100), 1)), (0, ((165728, 100), 1)), (2, ((161919, 100), 1)), (3, ((164430, 100), 1)), (3, ((169074, 100), 1)), (2, ((163151, 100), 1)), (2, ((167397, 100), 1))], [(4, ((169789, 100), 1)), (2, ((166893, 100), 1)), (4, ((163435, 100), 1)), (3, ((162578, 100), 1)), (3, ((166761, 100), 1))], [(1, ((166418, 100), 1)), (2, ((164493, 100), 1)), (3, ((171162, 100), 1)), (0, ((172483, 100), 1)), (1, ((167057, 100), 1)), (4, ((166761, 100), 1)), (3, ((171858, 100), 1)), (0, ((164248, 100), 1)), (4, ((167328, 100), 1)), (0, ((164070, 100), 1)), (2, ((169882, 100), 1)), (3, ((173658, 100), 1))], [(3, ((169306, 100), 1)), (2, ((167822, 100), 1)), (2, ((175267, 100), 1))], [(4, ((173436, 100), 1)), (2, ((170587, 100), 1)), (4, ((172877, 100), 1)), (4, ((173135, 100), 1)), (3, ((171263, 100), 1)), (2, ((169359, 100), 1)), (3, ((171243, 100), 1)), (1, ((172637, 100), 1)), (3, ((177534, 100), 1)), (3, ((176411, 100), 1))], [(1, ((177663, 100), 1)), (0, ((170984, 100), 1)), (3, ((171388, 100), 1)), (4, ((174935, 100), 1)), (4, ((174519, 100), 1)), (1, ((175416, 100), 1)), (3, ((174972, 100), 1)), (3, ((172246, 100), 1)), (2, ((176579, 100), 1)), (2, ((173556, 100), 1)), (0, ((173146, 100), 1)), (4, ((178446, 100), 1)), (1, ((170062, 100), 1))], [(3, ((177702, 100), 1))], [(0, ((175182, 100), 1)), (4, ((175053, 100), 1)), (0, ((175323, 100), 1)), (4, ((183174, 100), 1)), (3, ((183963, 100), 1)), (1, ((182478, 100), 1)), (0, ((183650, 100), 1)), (1, ((177931, 100), 1)), (2, ((175122, 100), 1)), (3, ((177166, 100), 1)), (4, ((180946, 100), 1)), (0, ((178211, 100), 1)), (2, ((177270, 100), 1)), (3, ((181360, 100), 1)), (4, ((179481, 100), 1)), (4, ((180449, 100), 1)), (2, ((176725, 100), 1)), (2, ((174305, 100), 1))], [(0, ((177444, 100), 1)), (4, ((184505, 100), 1)), (0, ((180619, 100), 1)), (3, ((185645, 100), 1)), (4, ((180102, 100), 1)), (1, ((176431, 100), 1)), (1, ((180859, 100), 1)), (1, ((176859, 100), 1)), (0, ((185327, 100), 1)), (1, ((181291, 100), 1)), (0, ((179446, 100), 1))], [(1, ((187979, 100), 1)), (0, ((186692, 100), 1))], [(3, ((182342, 100), 1)), (2, ((188934, 100), 1)), (3, ((187386, 100), 1)), (3, ((184929, 100), 1)), (0, ((180554, 100), 1)), (0, ((183246, 100), 1)), (0, ((180135, 100), 1))], [(4, ((185740, 100), 1)), (4, ((186471, 100), 1))], [(2, ((190515, 100), 1)), (0, ((193004, 100), 1)), (1, ((186957, 100), 1)), (1, ((190296, 100), 1)), (0, ((186654, 100), 1)), (0, ((184355, 100), 1)), (3, ((190337, 100), 1)), (1, ((187098, 100), 1)), (4, ((192721, 100), 1)), (0, ((186104, 100), 1)), (1, ((186935, 100), 1)), (0, ((193331, 100), 1)), (4, ((191089, 100), 1)), (3, ((189176, 100), 1))], [(3, ((187319, 100), 1)), (0, ((192970, 100), 1)), (2, ((187507, 100), 1)), (3, ((190928, 100), 1)), (1, ((194225, 100), 1)), (3, ((194819, 100), 1)), (1, ((189015, 100), 1)), (4, ((186696, 100), 1)), (2, ((187756, 100), 1)), (3, ((194322, 100), 1))], [(1, ((190175, 100), 1)), (3, ((192336, 100), 1)), (2, ((194073, 100), 1)), (4, ((189896, 100), 1)), (1, ((191850, 100), 1)), (3, ((188063, 100), 1)), (3, ((190426, 100), 1)), (1, ((189338, 100), 1)), (2, ((188371, 100), 1)), (4, ((192390, 100), 1)), (3, ((195610, 100), 1))], [(3, ((199545, 100), 1)), (0, ((197839, 100), 1)), (2, ((192660, 100), 1)), (3, ((192860, 100), 1)), (4, ((193008, 100), 1)), (4, ((199595, 100), 1)), (0, ((192800, 100), 1)), (1, ((195137, 100), 1)), (2, ((196936, 100), 1)), (4, ((196194, 100), 1)), (1, ((198068, 100), 1)), (0, ((194807, 100), 1)), (0, ((199879, 100), 1))], [(4, ((198401, 100), 1))], [(1, ((203279, 100), 1)), (4, ((195130, 100), 1)), (3, ((198659, 100), 1)), (0, ((199918, 100), 1)), (1, ((203117, 100), 1)), (3, ((202226, 100), 1)), (1, ((198278, 100), 1)), (0, ((199750, 100), 1)), (2, ((198747, 100), 1)), (1, ((195425, 100), 1)), (4, ((201356, 100), 1)), (4, ((201340, 100), 1)), (4, ((195508, 100), 1)), (4, ((199149, 100), 1)), (2, ((203174, 100), 1)), (2, ((201232, 100), 1)), (0, ((199294, 100), 1)), (3, ((196039, 100), 1))], [(0, ((203641, 100), 1)), (0, ((205522, 100), 1)), (1, ((204566, 100), 1)), (1, ((199169, 100), 1))], [(3, ((198731, 100), 1)), (2, ((207334, 100), 1)), (3, ((202829, 100), 1)), (1, ((201921, 100), 1)), (2, ((199018, 100), 1)), (3, ((205307, 100), 1)), (0, ((205990, 100), 1))], [(1, ((201374, 100), 1)), (2, ((209385, 100), 1)), (0, ((203786, 100), 1)), (2, ((200388, 100), 1)), (1, ((204152, 100), 1)), (1, ((203505, 100), 1)), (1, ((200264, 100), 1)), (0, ((207067, 100), 1)), (2, ((203313, 100), 1)), (0, ((200529, 100), 1)), (0, ((202756, 100), 1)), (3, ((209154, 100), 1)), (3, ((204266, 100), 1)), (3, ((203532, 100), 1)), (2, ((203693, 100), 1)), (4, ((206903, 100), 1)), (0, ((201327, 100), 1)), (2, ((205225, 100), 1)), (3, ((204405, 100), 1))], [(1, ((206464, 100), 1)), (1, ((210758, 100), 1)), (3, ((202634, 100), 1)), (1, ((204898, 100), 1)), (0, ((211068, 100), 1)), (4, ((211979, 100), 1)), (0, ((204127, 100), 1)), (0, ((208395, 100), 1)), (3, ((202727, 100), 1)), (4, ((207696, 100), 1)), (1, ((204544, 100), 1)), (1, ((206263, 100), 1)), (3, ((207756, 100), 1)), (3, ((206123, 100), 1)), (3, ((204186, 100), 1)), (2, ((202673, 100), 1)), (2, ((210540, 100), 1)), (4, ((207308, 100), 1)), (4, ((204783, 100), 1))], [(4, ((212078, 100), 1)), (4, ((204276, 100), 1)), (4, ((205860, 100), 1)), (0, ((204502, 100), 1))], [(4, ((213872, 100), 1)), (4, ((214261, 100), 1)), (4, ((207012, 100), 1)), (2, ((210258, 100), 1)), (2, ((211263, 100), 1)), (3, ((212242, 100), 1)), (2, ((213379, 100), 1)), (0, ((211244, 100), 1)), (2, ((210339, 100), 1)), (1, ((211361, 100), 1)), (3, ((207533, 100), 1)), (4, ((215222, 100), 1)), (0, ((214685, 100), 1)), (2, ((209714, 100), 1)), (0, ((213794, 100), 1)), (3, ((214247, 100), 1)), (1, ((208689, 100), 1)), (1, ((212647, 100), 1)), (4, ((206931, 100), 1))], [(1, ((212658, 100), 1)), (4, ((217276, 100), 1)), (0, ((217327, 100), 1)), (1, ((214844, 100), 1)), (3, ((214364, 100), 1)), (2, ((210439, 100), 1)), (3, ((209856, 100), 1)), (3, ((210452, 100), 1)), (1, ((212531, 100), 1)), (4, ((217851, 100), 1)), (1, ((215206, 100), 1)), (3, ((213878, 100), 1)), (3, ((209743, 100), 1))], [(1, ((219052, 100), 1)), (0, ((216472, 100), 1)), (2, ((214271, 100), 1)), (0, ((210145, 100), 1)), (2, ((216561, 100), 1)), (3, ((214488, 100), 1)), (4, ((213190, 100), 1)), (4, ((214230, 100), 1)), (3, ((212958, 100), 1)), (4, ((214447, 100), 1)), (0, ((216367, 100), 1)), (2, ((217667, 100), 1)), (3, ((212754, 100), 1)), (0, ((219152, 100), 1)), (1, ((212592, 100), 1))], [(1, ((221868, 100), 1)), (1, ((219460, 100), 1)), (1, ((215291, 100), 1)), (0, ((217801, 100), 1)), (2, ((214336, 100), 1)), (3, ((217300, 100), 1))], [(4, ((221422, 100), 1)), (4, ((222095, 100), 1)), (4, ((218683, 100), 1)), (4, ((215867, 100), 1)), (3, ((215390, 100), 1)), (4, ((218444, 100), 1))], [(1, ((217822, 100), 1)), (0, ((217649, 100), 1)), (2, ((220944, 100), 1)), (3, ((222399, 100), 1)), (0, ((225098, 100), 1)), (2, ((219791, 100), 1)), (0, ((220594, 100), 1)), (2, ((222922, 100), 1)), (1, ((221066, 100), 1)), (2, ((218154, 100), 1)), (3, ((217527, 100), 1)), (3, ((224774, 100), 1)), (1, ((217551, 100), 1)), (3, ((220666, 100), 1)), (1, ((218435, 100), 1)), (0, ((220598, 100), 1)), (1, ((217172, 100), 1)), (3, ((219016, 100), 1)), (2, ((219013, 100), 1))], [(4, ((222051, 100), 1)), (3, ((225336, 100), 1)), (0, ((222396, 100), 1)), (2, ((226324, 100), 1)), (0, ((225275, 100), 1)), (1, ((227059, 100), 1)), (3, ((220591, 100), 1)), (4, ((225777, 100), 1)), (1, ((220097, 100), 1)), (0, ((224835, 100), 1)), (4, ((219109, 100), 1)), (4, ((226086, 100), 1)), (2, ((221887, 100), 1)), (3, ((222022, 100), 1))], [(1, ((225144, 100), 1)), (1, ((228881, 100), 1)), (0, ((220982, 100), 1))], [(4, ((224465, 100), 1)), (2, ((231716, 100), 1))], [(1, ((225669, 100), 1)), (0, ((226927, 100), 1)), (1, ((226624, 100), 1)), (1, ((228324, 100), 1)), (4, ((224320, 100), 1)), (1, ((232424, 100), 1)), (3, ((225060, 100), 1)), (1, ((230776, 100), 1)), (1, ((230050, 100), 1)), (3, ((233087, 100), 1)), (2, ((226892, 100), 1)), (2, ((233072, 100), 1)), (4, ((228041, 100), 1)), (1, ((229943, 100), 1))], [], [], [(4, ((233557, 100), 1)), (2, ((236428, 100), 1)), (3, ((235994, 100), 1))], [(2, ((233764, 100), 1)), (0, ((232286, 100), 1)), (1, ((236745, 100), 1)), (0, ((239177, 100), 1)), (1, ((234325, 100), 1))], [(1, ((239032, 100), 1)), (0, ((239659, 100), 1)), (2, ((234168, 100), 1)), (0, ((239557, 100), 1)), (3, ((239335, 100), 1)), (1, ((236586, 100), 1)), (0, ((240962, 100), 1)), (4, ((241306, 100), 1)), (1, ((234543, 100), 1)), (1, ((242096, 100), 1)), (3, ((242430, 100), 1)), (4, ((237375, 100), 1)), (2, ((236678, 100), 1)), (3, ((242200, 100), 1)), (0, ((235845, 100), 1)), (1, ((242726, 100), 1))], [(0, ((236823, 100), 1)), (1, ((239472, 100), 1)), (3, ((243157, 100), 1)), (0, ((237603, 100), 1))], [(3, ((241942, 100), 1)), (0, ((245509, 100), 1)), (3, ((244945, 100), 1)), (4, ((241934, 100), 1)), (0, ((244454, 100), 1)), (0, ((242338, 100), 1))], [(0, ((244377, 100), 1)), (2, ((240599, 100), 1)), (1, ((246958, 100), 1)), (0, ((241975, 100), 1)), (4, ((244274, 100), 1)), (3, ((241008, 100), 1)), (2, ((246481, 100), 1)), (0, ((244986, 100), 1)), (3, ((247944, 100), 1)), (1, ((242271, 100), 1)), (0, ((243198, 100), 1)), (0, ((246665, 100), 1)), (0, ((242764, 100), 1)), (2, ((242778, 100), 1)), (4, ((249519, 100), 1)), (0, ((240044, 100), 1)), (0, ((248323, 100), 1)), (1, ((249189, 100), 1))], [(1, ((249903, 100), 1)), (2, ((251706, 100), 1))], [(4, ((250926, 100), 1)), (2, ((246452, 100), 1)), (3, ((247274, 100), 1)), (0, ((253973, 100), 1)), (2, ((247544, 100), 1)), (1, ((246500, 100), 1)), (1, ((251463, 100), 1)), (2, ((246211, 100), 1)), (1, ((248719, 100), 1)), (4, ((253361, 100), 1)), (4, ((248232, 100), 1)), (0, ((247949, 100), 1)), (1, ((246183, 100), 1)), (1, ((245679, 100), 1)), (1, ((251135, 100), 1)), (4, ((251391, 100), 1)), (3, ((249601, 100), 1)), (3, ((250031, 100), 1)), (3, ((247897, 100), 1))], [(1, ((247844, 100), 1)), (0, ((249088, 100), 1)), (3, ((255143, 100), 1)), (4, ((248285, 100), 1)), (2, ((255043, 100), 1)), (2, ((254017, 100), 1)), (2, ((255806, 100), 1)), (4, ((251421, 100), 1)), (1, ((251889, 100), 1)), (4, ((246944, 100), 1)), (0, ((254762, 100), 1)), (0, ((247256, 100), 1)), (4, ((251903, 100), 1)), (1, ((252796, 100), 1)), (2, ((255352, 100), 1))], [(3, ((248931, 100), 1))], [(4, ((257247, 100), 1)), (1, ((251058, 100), 1)), (2, ((259437, 100), 1)), (4, ((257340, 100), 1)), (2, ((259520, 100), 1)), (0, ((250191, 100), 1)), (2, ((256409, 100), 1)), (0, ((259537, 100), 1)), (0, ((252068, 100), 1)), (1, ((251712, 100), 1)), (4, ((251273, 100), 1)), (3, ((259137, 100), 1)), (1, ((256297, 100), 1)), (0, ((259374, 100), 1)), (3, ((252444, 100), 1)), (1, ((251762, 100), 1))], [(3, ((261674, 100), 1)), (2, ((255696, 100), 1)), (3, ((255455, 100), 1)), (1, ((255679, 100), 1)), (2, ((252542, 100), 1)), (0, ((258374, 100), 1)), (1, ((256284, 100), 1)), (3, ((253401, 100), 1)), (2, ((260380, 100), 1)), (1, ((260983, 100), 1)), (3, ((253134, 100), 1)), (3, ((259255, 100), 1))], [(2, ((262292, 100), 1)), (4, ((257852, 100), 1)), (3, ((257170, 100), 1)), (2, ((255090, 100), 1))], [(3, ((258738, 100), 1)), (2, ((261398, 100), 1)), (3, ((264619, 100), 1)), (1, ((261883, 100), 1)), (3, ((256093, 100), 1)), (2, ((262004, 100), 1)), (1, ((264975, 100), 1))], [(3, ((258297, 100), 1)), (3, ((260506, 100), 1)), (4, ((267134, 100), 1)), (4, ((262032, 100), 1)), (2, ((262050, 100), 1)), (0, ((265819, 100), 1)), (4, ((262189, 100), 1)), (2, ((267752, 100), 1)), (3, ((262752, 100), 1)), (4, ((261950, 100), 1)), (4, ((265695, 100), 1)), (0, ((262699, 100), 1)), (0, ((266303, 100), 1)), (1, ((260782, 100), 1)), (3, ((267051, 100), 1)), (2, ((260187, 100), 1)), (4, ((265119, 100), 1)), (4, ((264763, 100), 1)), (1, ((259681, 100), 1))], [(2, ((267696, 100), 1)), (2, ((267537, 100), 1)), (4, ((269199, 100), 1)), (0, ((261333, 100), 1))], [(2, ((266360, 100), 1)), (4, ((266743, 100), 1)), (4, ((267834, 100), 1)), (2, ((264199, 100), 1)), (4, ((269744, 100), 1)), (0, ((264600, 100), 1)), (3, ((264207, 100), 1)), (4, ((270757, 100), 1)), (2, ((267379, 100), 1)), (0, ((264966, 100), 1)), (4, ((263543, 100), 1)), (1, ((264313, 100), 1)), (4, ((263535, 100), 1)), (0, ((270788, 100), 1)), (4, ((270675, 100), 1)), (3, ((268811, 100), 1)), (4, ((267712, 100), 1)), (3, ((267766, 100), 1))], [], [(4, ((275774, 100), 1)), (4, ((267970, 100), 1)), (0, ((268345, 100), 1)), (1, ((275480, 100), 1))], [(3, ((270786, 100), 1)), (1, ((272689, 100), 1)), (0, ((277717, 100), 1)), (3, ((273155, 100), 1)), (0, ((276397, 100), 1)), (4, ((273000, 100), 1)), (4, ((275047, 100), 1)), (1, ((269811, 100), 1)), (3, ((274252, 100), 1)), (2, ((275083, 100), 1)), (0, ((276383, 100), 1)), (3, ((269373, 100), 1)), (0, ((273836, 100), 1)), (0, ((275256, 100), 1)), (0, ((274798, 100), 1)), (3, ((272366, 100), 1)), (4, ((268873, 100), 1)), (2, ((277569, 100), 1)), (2, ((277290, 100), 1))], [(0, ((277559, 100), 1)), (0, ((271649, 100), 1)), (2, ((275209, 100), 1)), (3, ((272742, 100), 1)), (2, ((270155, 100), 1)), (2, ((278400, 100), 1)), (1, ((270854, 100), 1)), (4, ((277443, 100), 1)), (3, ((276159, 100), 1)), (2, ((273583, 100), 1)), (4, ((278682, 100), 1)), (2, ((275795, 100), 1)), (0, ((271755, 100), 1))], [(2, ((277342, 100), 1)), (3, ((279461, 100), 1)), (1, ((280801, 100), 1)), (3, ((281268, 100), 1)), (2, ((277834, 100), 1)), (0, ((280036, 100), 1)), (4, ((274708, 100), 1)), (4, ((277181, 100), 1))], [(3, ((275130, 100), 1)), (1, ((280669, 100), 1)), (2, ((274101, 100), 1)), (4, ((274556, 100), 1)), (2, ((281311, 100), 1))], [(0, ((279862, 100), 1)), (1, ((276975, 100), 1)), (2, ((280266, 100), 1)), (4, ((276947, 100), 1)), (0, ((281453, 100), 1)), (1, ((279802, 100), 1)), (1, ((277524, 100), 1)), (0, ((277429, 100), 1)), (0, ((285581, 100), 1)), (2, ((283198, 100), 1)), (1, ((279415, 100), 1)), (3, ((284769, 100), 1))], [(3, ((281066, 100), 1)), (3, ((286267, 100), 1))], [(4, ((283849, 100), 1)), (3, ((284748, 100), 1)), (4, ((280396, 100), 1)), (3, ((286497, 100), 1)), (1, ((288058, 100), 1)), (0, ((284503, 100), 1)), (3, ((284096, 100), 1)), (1, ((283659, 100), 1)), (0, ((280549, 100), 1)), (3, ((281941, 100), 1)), (3, ((281241, 100), 1)), (4, ((281794, 100), 1)), (4, ((289933, 100), 1)), (2, ((280553, 100), 1)), (4, ((288583, 100), 1)), (1, ((280274, 100), 1)), (4, ((288981, 100), 1)), (1, ((289075, 100), 1))], [(2, ((288209, 100), 1)), (3, ((287700, 100), 1)), (2, ((282309, 100), 1)), (4, ((282363, 100), 1)), (0, ((284240, 100), 1))], [], [(4, ((295585, 100), 1)), (0, ((289646, 100), 1)), (4, ((288302, 100), 1)), (4, ((286990, 100), 1)), (2, ((287550, 100), 1)), (4, ((287426, 100), 1)), (1, ((294892, 100), 1)), (2, ((293346, 100), 1)), (4, ((290631, 100), 1)), (0, ((288990, 100), 1)), (0, ((295361, 100), 1)), (0, ((287079, 100), 1)), (3, ((293928, 100), 1))], [(3, ((289075, 100), 1)), (3, ((297447, 100), 1)), (0, ((290830, 100), 1)), (0, ((288632, 100), 1)), (1, ((290245, 100), 1)), (2, ((290413, 100), 1))], [(0, ((297322, 100), 1)), (2, ((298653, 100), 1)), (2, ((295161, 100), 1)), (0, ((297954, 100), 1)), (0, ((296419, 100), 1)), (2, ((292500, 100), 1)), (4, ((298096, 100), 1)), (3, ((296444, 100), 1)), (2, ((296747, 100), 1)), (1, ((299759, 100), 1)), (2, ((291494, 100), 1)), (4, ((293706, 100), 1))], [(4, ((300671, 100), 1)), (2, ((296636, 100), 1)), (2, ((294327, 100), 1)), (3, ((292452, 100), 1)), (0, ((294072, 100), 1)), (0, ((293575, 100), 1)), (0, ((295636, 100), 1)), (2, ((294355, 100), 1)), (2, ((298136, 100), 1)), (3, ((300981, 100), 1)), (4, ((293856, 100), 1)), (4, ((294207, 100), 1)), (4, ((294833, 100), 1)), (4, ((296075, 100), 1))], [(2, ((297412, 100), 1)), (2, ((298660, 100), 1)), (3, ((301629, 100), 1)), (2, ((296316, 100), 1)), (0, ((296114, 100), 1)), (0, ((301079, 100), 1)), (2, ((301758, 100), 1)), (0, ((297567, 100), 1)), (1, ((303829, 100), 1))], [(3, ((305007, 100), 1)), (3, ((298010, 100), 1)), (3, ((305468, 100), 1)), (3, ((299105, 100), 1))], [(0, ((298669, 100), 1)), (0, ((303910, 100), 1)), (4, ((301299, 100), 1)), (2, ((304844, 100), 1)), (3, ((305575, 100), 1)), (1, ((298533, 100), 1)), (2, ((304569, 100), 1)), (0, ((302147, 100), 1)), (3, ((306547, 100), 1)), (3, ((300842, 100), 1)), (0, ((302441, 100), 1)), (0, ((304653, 100), 1))], [(4, ((300793, 100), 1)), (0, ((304423, 100), 1)), (4, ((300880, 100), 1)), (4, ((305934, 100), 1)), (4, ((302728, 100), 1)), (4, ((306064, 100), 1)), (3, ((305264, 100), 1)), (0, ((305124, 100), 1)), (4, ((303994, 100), 1)), (1, ((301749, 100), 1)), (4, ((302435, 100), 1)), (4, ((302954, 100), 1)), (0, ((302035, 100), 1)), (1, ((304224, 100), 1)), (0, ((306361, 100), 1)), (2, ((305682, 100), 1)), (4, ((307333, 100), 1)), (2, ((305385, 100), 1))], [(0, ((302279, 100), 1)), (4, ((309637, 100), 1)), (4, ((308892, 100), 1)), (1, ((307806, 100), 1)), (4, ((309263, 100), 1)), (2, ((306298, 100), 1)), (4, ((310534, 100), 1)), (3, ((311900, 100), 1)), (4, ((311522, 100), 1)), (4, ((303152, 100), 1)), (3, ((305304, 100), 1))], [(0, ((304754, 100), 1)), (2, ((305255, 100), 1)), (0, ((312420, 100), 1)), (0, ((313758, 100), 1)), (4, ((305321, 100), 1)), (1, ((309905, 100), 1))], [(4, ((309315, 100), 1))], [(1, ((310857, 100), 1)), (4, ((308587, 100), 1)), (1, ((317197, 100), 1)), (0, ((315642, 100), 1)), (3, ((315781, 100), 1)), (0, ((316836, 100), 1)), (0, ((317642, 100), 1)), (1, ((308061, 100), 1)), (1, ((312265, 100), 1)), (0, ((313578, 100), 1))], [(2, ((314279, 100), 1)), (1, ((311217, 100), 1)), (3, ((314532, 100), 1)), (3, ((315853, 100), 1)), (4, ((316578, 100), 1)), (2, ((317895, 100), 1)), (3, ((317988, 100), 1)), (2, ((310663, 100), 1)), (3, ((317105, 100), 1))], [(3, ((316941, 100), 1)), (4, ((321389, 100), 1)), (0, ((318206, 100), 1)), (3, ((316224, 100), 1)), (1, ((312276, 100), 1)), (3, ((313284, 100), 1)), (0, ((317813, 100), 1)), (1, ((313086, 100), 1)), (2, ((320106, 100), 1)), (2, ((317416, 100), 1)), (4, ((315902, 100), 1)), (3, ((313930, 100), 1)), (2, ((315723, 100), 1)), (2, ((319513, 100), 1)), (3, ((315296, 100), 1)), (1, ((314227, 100), 1)), (4, ((315518, 100), 1)), (2, ((320576, 100), 1))], [(3, ((322516, 100), 1)), (2, ((314274, 100), 1))], [(1, ((319168, 100), 1)), (1, ((323466, 100), 1)), (0, ((321205, 100), 1)), (4, ((323097, 100), 1)), (1, ((322471, 100), 1)), (4, ((318844, 100), 1)), (1, ((324640, 100), 1)), (3, ((320942, 100), 1)), (4, ((324811, 100), 1)), (4, ((320121, 100), 1)), (2, ((325251, 100), 1)), (0, ((325481, 100), 1)), (4, ((319627, 100), 1))], [(3, ((319770, 100), 1))], [(0, ((328620, 100), 1)), (0, ((321825, 100), 1)), (0, ((325834, 100), 1))], [(0, ((327903, 100), 1)), (0, ((322667, 100), 1))], [(3, ((324971, 100), 1)), (3, ((333374, 100), 1)), (1, ((325719, 100), 1))], [(2, ((330681, 100), 1)), (0, ((331172, 100), 1)), (2, ((332295, 100), 1)), (1, ((332161, 100), 1)), (2, ((327880, 100), 1)), (4, ((335289, 100), 1)), (1, ((333462, 100), 1)), (3, ((334874, 100), 1)), (2, ((332811, 100), 1))], [(4, ((333449, 100), 1)), (2, ((329994, 100), 1)), (2, ((328629, 100), 1)), (4, ((333077, 100), 1)), (3, ((336245, 100), 1)), (0, ((334758, 100), 1)), (1, ((336437, 100), 1)), (4, ((331790, 100), 1)), (4, ((336563, 100), 1)), (3, ((333566, 100), 1)), (3, ((335455, 100), 1)), (1, ((335761, 100), 1)), (1, ((329060, 100), 1)), (4, ((332751, 100), 1))], [(2, ((338616, 100), 1)), (0, ((337858, 100), 1)), (2, ((334156, 100), 1)), (1, ((339599, 100), 1))], [(3, ((336094, 100), 1)), (0, ((337103, 100), 1)), (4, ((333061, 100), 1)), (1, ((337125, 100), 1)), (2, ((336250, 100), 1)), (4, ((333918, 100), 1)), (4, ((334181, 100), 1)), (2, ((337355, 100), 1)), (1, ((338352, 100), 1)), (3, ((340436, 100), 1)), (2, ((337057, 100), 1)), (0, ((334693, 100), 1)), (2, ((337388, 100), 1))], [(3, ((334772, 100), 1)), (0, ((342475, 100), 1)), (0, ((340545, 100), 1)), (2, ((338549, 100), 1)), (3, ((336636, 100), 1)), (2, ((342834, 100), 1)), (0, ((340093, 100), 1)), (2, ((342719, 100), 1)), (1, ((334014, 100), 1))], [(2, ((343271, 100), 1)), (0, ((343246, 100), 1)), (0, ((345506, 100), 1)), (0, ((338564, 100), 1)), (4, ((341876, 100), 1)), (2, ((337020, 100), 1)), (0, ((343165, 100), 1)), (1, ((338299, 100), 1)), (4, ((345549, 100), 1)), (1, ((337550, 100), 1)), (4, ((339616, 100), 1)), (3, ((340113, 100), 1)), (3, ((343702, 100), 1)), (4, ((340875, 100), 1)), (1, ((344936, 100), 1)), (0, ((337532, 100), 1)), (2, ((344407, 100), 1))], [(2, ((344826, 100), 1)), (1, ((339988, 100), 1)), (4, ((339139, 100), 1)), (1, ((347311, 100), 1)), (0, ((343867, 100), 1)), (3, ((342368, 100), 1)), (4, ((346687, 100), 1)), (2, ((345875, 100), 1)), (1, ((340977, 100), 1)), (3, ((344549, 100), 1)), (4, ((343749, 100), 1)), (0, ((338792, 100), 1)), (1, ((343074, 100), 1)), (1, ((342766, 100), 1)), (4, ((345630, 100), 1)), (1, ((344223, 100), 1)), (1, ((340689, 100), 1))], [(3, ((349317, 100), 1)), (2, ((346647, 100), 1)), (2, ((344606, 100), 1))], [], [(0, ((345459, 100), 1)), (4, ((349580, 100), 1))], [(3, ((349230, 100), 1)), (4, ((347107, 100), 1)), (3, ((351855, 100), 1)), (3, ((352922, 100), 1)), (3, ((355595, 100), 1)), (0, ((354827, 100), 1)), (0, ((348739, 100), 1)), (2, ((347115, 100), 1)), (1, ((354026, 100), 1)), (1, ((346823, 100), 1)), (3, ((350545, 100), 1)), (3, ((348746, 100), 1)), (3, ((352745, 100), 1)), (1, ((349179, 100), 1)), (0, ((348039, 100), 1)), (3, ((351421, 100), 1)), (1, ((355925, 100), 1)), (3, ((347026, 100), 1))], [(0, ((350516, 100), 1)), (3, ((354624, 100), 1)), (4, ((350565, 100), 1)), (4, ((357205, 100), 1)), (4, ((348195, 100), 1)), (3, ((354629, 100), 1)), (3, ((354651, 100), 1))], [(3, ((354261, 100), 1)), (0, ((358246, 100), 1)), (4, ((351854, 100), 1)), (3, ((351119, 100), 1)), (1, ((351434, 100), 1)), (0, ((355003, 100), 1)), (4, ((354273, 100), 1)), (0, ((350910, 100), 1)), (0, ((359633, 100), 1)), (1, ((358370, 100), 1)), (3, ((351148, 100), 1)), (1, ((358232, 100), 1)), (3, ((356415, 100), 1)), (0, ((357350, 100), 1)), (1, ((357549, 100), 1)), (0, ((355508, 100), 1)), (1, ((351770, 100), 1)), (0, ((355586, 100), 1)), (1, ((353933, 100), 1))], [(0, ((353443, 100), 1)), (0, ((356002, 100), 1)), (0, ((352107, 100), 1)), (1, ((360238, 100), 1)), (3, ((359665, 100), 1)), (1, ((352625, 100), 1)), (0, ((352157, 100), 1)), (4, ((352270, 100), 1)), (3, ((361319, 100), 1)), (3, ((355140, 100), 1)), (3, ((360058, 100), 1)), (0, ((355438, 100), 1)), (2, ((355625, 100), 1)), (4, ((353351, 100), 1)), (3, ((360539, 100), 1)), (3, ((357038, 100), 1))], [(4, ((360247, 100), 1)), (0, ((359998, 100), 1)), (0, ((355556, 100), 1)), (2, ((361174, 100), 1)), (3, ((356503, 100), 1)), (0, ((363300, 100), 1)), (4, ((359840, 100), 1)), (4, ((358914, 100), 1)), (2, ((354599, 100), 1)), (1, ((359981, 100), 1)), (1, ((361800, 100), 1)), (4, ((356498, 100), 1)), (0, ((361106, 100), 1)), (3, ((360862, 100), 1)), (3, ((360377, 100), 1)), (2, ((361589, 100), 1)), (3, ((356715, 100), 1))], [(3, ((362256, 100), 1)), (1, ((360213, 100), 1)), (4, ((361746, 100), 1)), (4, ((363387, 100), 1)), (0, ((357806, 100), 1)), (3, ((365805, 100), 1)), (0, ((359046, 100), 1)), (0, ((360911, 100), 1)), (0, ((360896, 100), 1))], [(3, ((359472, 100), 1)), (3, ((364215, 100), 1)), (2, ((366421, 100), 1)), (0, ((366977, 100), 1)), (3, ((358369, 100), 1)), (0, ((361940, 100), 1)), (2, ((362242, 100), 1))], [], [], [(0, ((366108, 100), 1)), (1, ((369462, 100), 1)), (0, ((364543, 100), 1)), (1, ((372631, 100), 1)), (3, ((365580, 100), 1)), (3, ((368849, 100), 1)), (3, ((367859, 100), 1)), (3, ((367239, 100), 1)), (2, ((371921, 100), 1)), (1, ((367520, 100), 1)), (1, ((364908, 100), 1)), (0, ((371743, 100), 1)), (0, ((372708, 100), 1)), (0, ((368916, 100), 1)), (3, ((368654, 100), 1))], [(2, ((373241, 100), 1)), (1, ((367506, 100), 1)), (3, ((372207, 100), 1)), (1, ((367427, 100), 1)), (4, ((367871, 100), 1)), (2, ((366546, 100), 1)), (0, ((370044, 100), 1)), (1, ((370159, 100), 1)), (3, ((374625, 100), 1)), (2, ((371227, 100), 1)), (3, ((366409, 100), 1)), (2, ((368759, 100), 1)), (2, ((369155, 100), 1)), (1, ((375268, 100), 1)), (0, ((375605, 100), 1))], [(1, ((370604, 100), 1)), (0, ((369964, 100), 1)), (4, ((371383, 100), 1)), (0, ((376219, 100), 1)), (3, ((373433, 100), 1)), (0, ((370211, 100), 1))], [(0, ((379724, 100), 1)), (4, ((378494, 100), 1)), (2, ((373644, 100), 1)), (1, ((375743, 100), 1)), (1, ((377529, 100), 1)), (1, ((377722, 100), 1)), (0, ((377324, 100), 1)), (4, ((371866, 100), 1)), (0, ((371552, 100), 1)), (3, ((370694, 100), 1)), (4, ((372530, 100), 1)), (4, ((370798, 100), 1))], [(2, ((373618, 100), 1)), (4, ((376388, 100), 1)), (4, ((379436, 100), 1)), (2, ((372371, 100), 1)), (4, ((372674, 100), 1)), (0, ((372489, 100), 1)), (1, ((380449, 100), 1))], [(1, ((378123, 100), 1)), (2, ((376734, 100), 1)), (2, ((379098, 100), 1)), (4, ((380334, 100), 1)), (3, ((379383, 100), 1)), (1, ((375301, 100), 1)), (4, ((377049, 100), 1))], [(2, ((385282, 100), 1)), (2, ((378219, 100), 1)), (2, ((376382, 100), 1))], [(0, ((383329, 100), 1)), (4, ((383725, 100), 1)), (1, ((382015, 100), 1)), (0, ((382845, 100), 1)), (2, ((380423, 100), 1)), (3, ((385665, 100), 1)), (0, ((380879, 100), 1)), (3, ((379131, 100), 1)), (4, ((382005, 100), 1)), (0, ((383913, 100), 1)), (1, ((380594, 100), 1)), (4, ((384108, 100), 1))], [(4, ((382209, 100), 1)), (0, ((380701, 100), 1)), (4, ((385488, 100), 1))], [(2, ((384179, 100), 1)), (1, ((385175, 100), 1)), (1, ((387194, 100), 1)), (4, ((382985, 100), 1)), (2, ((390471, 100), 1))], [(4, ((389504, 100), 1)), (1, ((387014, 100), 1)), (0, ((391231, 100), 1)), (3, ((392082, 100), 1)), (4, ((384724, 100), 1)), (2, ((385017, 100), 1)), (0, ((392740, 100), 1)), (0, ((386080, 100), 1)), (4, ((386651, 100), 1)), (2, ((384818, 100), 1)), (3, ((391304, 100), 1)), (1, ((391733, 100), 1)), (4, ((387831, 100), 1)), (1, ((391657, 100), 1)), (2, ((391148, 100), 1)), (1, ((388603, 100), 1)), (0, ((385202, 100), 1)), (1, ((386126, 100), 1))], [(0, ((390065, 100), 1)), (0, ((392327, 100), 1)), (2, ((387241, 100), 1)), (4, ((393775, 100), 1)), (3, ((386586, 100), 1)), (0, ((394633, 100), 1)), (2, ((390640, 100), 1)), (2, ((388829, 100), 1))], [(1, ((390078, 100), 1)), (2, ((390409, 100), 1)), (1, ((395360, 100), 1)), (1, ((392531, 100), 1)), (1, ((393788, 100), 1)), (0, ((394399, 100), 1)), (1, ((390660, 100), 1)), (1, ((394493, 100), 1)), (2, ((392400, 100), 1)), (2, ((393230, 100), 1)), (0, ((394403, 100), 1)), (0, ((395584, 100), 1)), (3, ((394299, 100), 1)), (3, ((392944, 100), 1))], [(0, ((398854, 100), 1)), (4, ((394727, 100), 1)), (0, ((394599, 100), 1)), (2, ((392496, 100), 1)), (2, ((399797, 100), 1)), (3, ((396545, 100), 1)), (4, ((397138, 100), 1)), (2, ((395912, 100), 1)), (2, ((397138, 100), 1)), (1, ((391318, 100), 1)), (1, ((392656, 100), 1)), (4, ((392782, 100), 1)), (0, ((396375, 100), 1)), (4, ((395531, 100), 1)), (2, ((398717, 100), 1)), (2, ((394957, 100), 1)), (1, ((390235, 100), 1)), (2, ((391295, 100), 1)), (2, ((394239, 100), 1))], [(1, ((398754, 100), 1))], [(3, ((397454, 100), 1)), (0, ((396851, 100), 1)), (3, ((398186, 100), 1)), (4, ((398539, 100), 1)), (1, ((400366, 100), 1)), (3, ((398609, 100), 1)), (2, ((401871, 100), 1)), (2, ((395256, 100), 1)), (1, ((402433, 100), 1)), (3, ((394565, 100), 1))], [(1, ((401279, 100), 1)), (0, ((402939, 100), 1)), (2, ((399106, 100), 1)), (4, ((400626, 100), 1))], [(0, ((401253, 100), 1))]]
