# Seeds for failure cases proptest has generated in the past. It is
# automatically read and these particular cases re-run before any
# novel cases are generated.
#
# It is recommended to check this file in to source control so that
# everyone who runs the test benefits from these saved cases.
cc a971688c62d00b4f928582888f0edc0965926d1aee2a60d860b94acd299e4cbc # shrinks to trace = [([(93, 2, 1), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (0, 0, 0), (93, 2, -1)], 1)]
cc d21710101a9d4899e562bd806ba3b30b373d6a1a5979f4a1e6a70295a29243df # shrinks to trace = [([(99, 0, 1), (8, 0, 1), (46, 0, 1), (15, 0, 1), (16, 0, 1), (2, 0, 1), (9, 0, 1), (45, 0, 1), (0, 0, 1)], 10)]
cc a00e02e9a3718a6e0f020923eaf3dd366bebd8aeaae233544721cf5b94a3e06a # shrinks to trace = [([(42, 0, 1), (0, 0, -1), (9, 0, -1), (15, 0, -1), (10, 0, 1), (28, 0, 1), (23, 0, -1), (11, 0, -1), (6, 0, -1)], 10)]
