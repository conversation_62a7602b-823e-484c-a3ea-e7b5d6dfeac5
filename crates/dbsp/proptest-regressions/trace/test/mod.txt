# Seeds for failure cases proptest has generated in the past. It is
# automatically read and these particular cases re-run before any
# novel cases are generated.
#
# It is recommended to check this file in to source control so that
# everyone who runs the test benefits from these saved cases.
cc 027d28e58a11ac16e7192491f81844853ea98fe3b3ecf179061f063e991433f3 # shrinks to batches = [([], 0, 0)], seed = 0
cc 250f91dcedad5d9e880cf6b72ba62c22d9180bddfc5e93df6d01214fe5939ec0 # shrinks to batches = [([(1, -1), (92, 0), (9, -2), (3, -2), (48, 1), (66, 1)], 32), ([(53, -2), (45, -1), (65, -1), (2, -2), (93, -1), (33, 0), (73, -1), (68, 0), (11, -2), (66, -1), (34, 1), (43, -1), (28, 1), (95, -2), (40, -1), (9, 0), (64, -2), (63, -2), (92, -1), (75, 0), (87, -1), (44, -2), (87, 0), (23, 0), (49, -1), (35, -2), (38, 0), (86, -1), (32, -2), (0, 0), (84, -1), (5, 0), (2, 0), (32, 0), (43, -1), (69, 0), (20, -2), (94, -2), (79, 0), (61, -2), (71, -2), (1, 0), (44, 1), (16, 1), (56, -1), (6, -1), (57, 1), (54, -2), (15, 1), (28, -2), (21, -2), (52, -1), (32, 0), (95, -1), (51, 1), (85, -2), (9, 0), (68, 1), (77, 1), (59, 1), (45, -2), (16, 1), (65, -1), (63, -2), (41, 1), (85, -1), (69, 0), (35, 1), (50, 1), (93, 1), (20, 0), (45, 1), (83, 0), (96, -2), (17, -2), (91, -1), (49, -2), (37, -2), (40, 1), (82, -2), (65, 0), (91, -2), (94, -2), (88, -1), (1, -1), (85, 1), (45, 0), (72, -2), (53, 0), (78, 0), (42, -1), (67, -1), (88, -1), (38, 0), (76, 1), (89, 1), (89, 0), (44, -2), (2, -1), (48, -1), (53, 1), (29, -1), (47, 1), (23, -2), (60, 0), (15, 1), (70, 0)], 29), ([(72, 1), (68, 1), (38, -1), (16, -2), (18, 0), (25, 1), (80, -2), (9, -1), (79, 1), (41, -2), (74, 1), (18, 1), (59, 0), (7, -1), (30, 0), (49, 1), (26, -2), (84, -2), (76, 1), (99, -1), (38, 0), (67, -2), (44, -2), (12, 1), (44, 0), (45, 0), (52, -2), (19, -1), (28, -2), (99, 1), (60, -1), (56, -1), (44, -1), (76, -2), (26, -2), (78, 1), (77, -2), (15, 1), (77, 0), (61, 1), (70, 1), (58, 1), (22, -2), (29, -1), (84, -2), (53, -2), (47, -2), (14, 0), (75, -2), (2, 1), (82, 0), (29, 1), (99, -2), (11, 0), (95, -2), (66, -1), (84, -2), (41, 0), (75, -2), (22, 0), (29, -1), (34, 1), (9, 0), (42, -2), (69, 0), (69, 0), (10, 0), (49, -2), (87, 1), (60, 1), (34, 0), (63, -1), (39, -2), (38, -2), (34, 1), (62, 1), (29, -1), (26, 1), (98, -1), (69, -1), (0, 1), (10, -2), (20, -1), (64, 0), (66, -2), (45, -1), (81, 0), (32, 0), (50, -1), (55, -1), (18, -1), (67, 1), (85, 1), (19, 0), (0, -1), (38, 0), (76, -1), (29, 1), (68, -2), (5, 0), (97, -1), (77, 0), (88, -1), (43, 1), (29, 1), (6, -2), (84, 1), (55, -1), (18, -1), (93, -1), (59, 1), (62, 0), (96, -1), (95, 1), (55, -2), (16, 0), (38, 1), (92, -2), (80, 0), (4, -1), (48, 1), (65, -2), (59, -2), (79, 0), (68, -2), (86, -1), (1, 1), (49, -1), (92, -1), (74, -2), (5, -2), (89, -2), (22, -2), (4, 0)], 85), ([(75, 0), (66, -2), (4, -2), (68, -1), (86, 0), (80, -1), (97, -1), (3, -1), (49, -1), (16, 0), (71, 0), (61, 0), (95, 0), (78, 1), (71, -2), (56, -2), (0, -2), (54, -2), (81, 0), (35, 1), (90, -1), (31, -1), (95, -2), (29, -2), (48, 1), (67, -2), (39, 1), (98, 0), (9, -1), (44, 0), (89, 0), (54, 1), (6, 0), (38, -1), (38, 0), (47, -1), (94, -2), (83, 1), (88, -1), (51, 0), (26, -2), (71, -1), (46, -2), (12, 1), (7, 0), (51, -2), (13, -1), (70, -1), (23, -1), (70, -1), (60, -2), (96, 0), (20, 1), (1, -2), (33, 0), (81, 0), (45, -1), (76, 0), (52, 0), (34, 1), (20, -2), (70, 1), (3, -2), (6, -2), (75, 1), (60, -1), (88, 1), (45, -2), (24, -1), (15, -2), (22, -2), (35, -1), (91, 1), (49, 1), (50, 1), (3, -2), (92, 1), (18, -1), (37, -2), (44, -2), (3, 1), (4, 1), (40, 0), (40, 0), (35, -2), (39, 0), (92, 1), (62, -1), (99, -2), (43, 1), (73, 1), (72, -2), (16, -2), (52, 0), (86, -2), (65, -2), (53, -1), (12, -1), (96, -2), (38, -2), (29, 1), (88, 1), (35, 1), (10, -1), (27, -2), (78, -1), (29, 1), (32, 1), (28, 1), (33, -1), (35, 1), (79, 1), (59, 0), (49, -2), (41, 0), (82, -2), (97, -2), (64, -2), (91, 1), (18, -2), (83, 0), (97, 1), (67, 1), (69, -2), (89, -1), (41, 0), (27, -1), (10, 0), (37, -2), (95, 1), (5, -1), (74, -1), (61, 0), (18, 1), (12, -2), (82, 0), (13, -1), (44, -2), (4, -1), (40, 1), (51, -1), (38, -2), (1, -1), (92, 0), (64, 0), (47, 1), (94, 1), (17, 0), (17, 1), (47, -2), (21, 1), (34, 0), (8, 0), (85, 1), (47, -2), (5, 0), (68, -2), (68, -2), (23, 1), (73, -1), (98, 0), (44, 0), (52, -2), (15, -2), (14, 0), (42, 1), (38, 0), (13, 1), (34, -2), (50, 1), (95, -1), (44, -2), (5, 0), (77, -2), (0, -2), (95, -2), (17, -2), (44, 1), (7, 0), (59, -2), (16, -2), (65, -2), (45, 0), (4, 0), (10, 1), (61, 0), (21, -1), (87, 0), (67, -2), (27, 1), (91, -1), (34, -2), (62, 1), (82, 1), (55, 0), (0, 0), (86, 0), (72, -2), (31, 0), (14, -2), (49, -1), (73, 1), (2, 1), (69, -2), (17, -2), (82, -2), (15, 1), (6, -2), (77, -1), (20, -1), (68, 1), (7, 1), (14, -1), (40, 0), (98, 1), (28, 0), (46, 1), (13, 1), (96, 0), (46, 0), (25, -1), (41, -1), (31, -1), (4, -2), (32, -1), (53, -2), (33, -1), (21, 0), (19, 1), (54, 0), (82, -1), (82, 1), (28, 0), (3, 1), (61, 0), (78, 1), (45, -2), (66, 1), (70, 1), (90, 1), (83, 1), (19, -2), (39, 0), (41, 1), (38, -1), (68, 1), (68, 0), (51, -1), (48, -2), (64, -2), (38, 0), (64, -1), (13, -1), (31, 1), (23, -1), (39, 1), (39, 0), (10, 0), (29, -2), (26, 0), (53, 0), (33, 0), (63, 0), (79, 0), (42, 0), (40, 1), (17, 0), (10, 1), (93, 1), (46, 0), (46, -2), (40, -2), (65, -1), (51, -1), (62, 0), (84, 0), (36, -2), (47, -1), (90, 1), (2, 0), (71, 1), (95, -2), (25, -1), (43, -2), (94, 1), (88, 0), (30, 0), (54, 0), (21, 1), (87, 1), (50, 0), (55, -2), (63, 1), (69, 0), (58, 1), (94, 0), (40, 0), (50, 0), (95, 0), (68, -1), (87, -2), (57, 1), (49, -2), (1, 1), (29, -1), (59, -1), (33, -1), (72, -2), (99, -2), (0, 0), (71, -2), (76, 1), (81, 1), (87, 0), (64, 1), (95, 0), (3, -1), (49, 0), (42, -2), (41, -1), (21, -1), (62, -2), (33, -2), (49, 1), (70, -2), (51, 1), (62, 0), (76, -1), (24, -2), (63, -1), (74, 1), (29, -1), (40, 0), (86, 1), (28, -2), (7, -2), (14, -2), (83, 1), (3, -1), (86, 0), (76, -2), (40, -1), (10, -1), (60, -1), (18, 0), (88, 1), (21, -1), (94, 0), (82, -2), (45, 0), (91, 1), (25, -2), (38, -1), (79, -2), (51, -2), (83, -2), (38, 0), (70, 0), (68, -1), (50, -1), (96, 1), (11, -1), (93, -2), (90, -2), (54, 1), (83, 1), (89, 1), (99, 1), (52, -1), (25, 1), (76, -2), (38, 0), (49, 1), (63, -1), (39, -1), (35, 0), (95, -2), (85, 0), (60, -1), (24, 1), (32, 0), (51, -1), (21, 0), (61, -1), (4, -1), (71, 0), (18, -1), (83, -2), (62, 0), (89, -1), (75, 0), (29, -1), (94, -2), (82, -1), (74, -1), (37, 0), (22, -2), (55, -1), (79, 0), (25, 0), (86, 0), (69, 1), (36, -2), (32, 1), (84, -2), (75, 0), (72, 1), (99, 0), (71, 1), (44, -1), (11, 1), (3, -1), (18, 1), (69, 1), (12, -2), (92, -2), (43, -2), (3, 0), (83, -2), (66, 0), (42, 1), (46, 0), (26, -2), (40, 0), (59, 0), (88, -1), (63, -2), (97, -1), (45, 1), (38, 1), (20, 1), (78, 0), (61, 0), (82, -1), (91, -1), (0, 1), (2, -2), (12, -2), (14, -1), (22, 0), (29, -1), (38, -2), (50, 1), (92, 1), (21, -2), (76, -1), (89, -2), (48, 0), (76, 1), (3, 1), (3, -1), (94, 0), (76, -2), (26, -2), (79, -1), (77, -1), (40, -2), (67, 1), (39, -1), (7, -2), (39, -1), (4, -2), (7, 0), (86, 1), (65, -1), (57, -2), (44, -2), (8, -1), (83, 0), (24, -2), (79, -1), (35, -1), (54, -2), (50, 0), (13, 0), (19, 1), (24, -2), (63, 0), (58, -1), (59, -2), (33, -1), (59, 0), (55, -1), (81, 0), (69, -2), (7, -2), (61, -1), (71, -2), (63, -2), (75, -2), (14, 1), (80, -2), (65, 1), (28, 0), (69, -2), (34, -1)], 40), ([(83, -2), (43, -1), (32, 1), (39, 0), (33, 0), (84, -1), (24, -1), (7, -2), (46, 0), (7, 1), (77, -1), (91, 1), (93, -1), (24, -2), (71, -1), (77, 1), (52, 1), (24, -1), (15, -1), (13, 0), (49, 0), (87, -1), (63, -2), (47, 0), (26, -2), (23, 1), (92, -2), (30, -2), (6, 1), (10, 0), (98, 0), (46, 0), (27, -2), (28, 1), (51, -1), (28, -1), (93, 1), (41, 0), (6, -1), (25, -2), (85, -1), (86, -1), (92, 1), (91, 0), (82, 0), (28, -1), (81, 1), (54, -2), (83, 0), (46, 1), (47, 1), (37, -1), (42, -1), (40, -1), (64, -2), (68, -2), (36, -1), (76, -2), (45, 1), (42, 1), (75, -2), (21, 0), (62, 0), (64, 1), (16, -2), (64, 1), (15, -2), (29, -2), (9, 0), (4, 0), (90, 0), (86, 1), (99, 0), (43, -1), (85, -1), (49, 1), (50, 0), (46, 0), (1, 1), (0, -1), (78, -2), (54, 0), (65, -2), (93, -1), (74, 1), (49, -1), (96, -1), (8, -1), (96, -2), (8, 1), (21, -1), (53, -2), (96, -1), (42, 0), (41, 0), (29, 0), (80, -2), (13, 0), (8, 1), (1, -1), (82, -2), (52, 1), (85, -1), (5, 0), (53, -2), (24, 1), (29, 0), (26, 1), (21, -1), (32, 0), (23, 1), (41, -1), (38, 0), (14, 1), (99, 0), (73, 0), (63, -1), (24, -1), (22, -2), (84, -2), (87, -2), (63, -1), (37, 1), (29, 1), (79, -2), (98, 0), (77, -2), (94, 1), (33, 1), (73, 0), (34, -2), (46, -1), (39, -1), (10, 1), (77, 1), (4, 0), (80, 0), (38, -1), (57, 0), (44, -1), (56, 1), (68, -2), (20, 0), (30, -1), (15, -2), (4, 1), (75, -1), (65, -2), (79, -2), (22, 1), (77, 1), (16, 0), (10, -2), (25, -2), (32, -1), (93, -1), (38, -2), (81, 0), (55, -2), (29, -2), (33, -1), (9, -1), (29, -2), (32, 1), (34, -1), (18, -2), (79, 1), (12, -1), (31, 0), (71, 0), (12, 1), (7, -2), (87, -2), (88, 0), (56, -2), (49, -2), (86, 0), (0, -2), (88, 0), (42, 0), (45, 1), (80, 1), (58, -2), (99, -1), (30, 1), (27, 1), (50, 1), (80, 1), (35, -1), (73, 1), (1, 0), (7, 0), (91, -1), (41, 0), (86, -1), (40, -1), (72, -1), (24, -1), (76, -2), (97, 1), (34, -2), (69, -2), (60, 1), (34, -2), (3, -1), (37, -2), (35, -2), (0, 0), (60, 0), (35, -2), (19, -2), (1, 1), (53, -1), (55, 0), (85, -1), (31, 1), (5, 1), (83, 1), (68, 1), (32, 0), (33, 1), (76, 1), (64, -1), (38, 0), (34, -1), (26, 0), (43, 0), (86, 0), (72, 1), (14, -1), (47, -1), (12, -2), (79, -1), (20, -2), (64, 1), (4, 0), (67, 1), (78, -1), (60, -2), (85, -2), (30, -1), (1, -1), (7, 0), (67, 0), (36, -2), (15, 1), (4, 0), (3, -2), (35, 1), (80, 1), (90, -1), (19, -2), (0, -2), (43, 1), (15, -2), (5, -2), (86, 0), (94, -1), (60, -1), (4, -1), (4, 1), (93, -2), (78, -1), (96, 1), (97, -1), (92, -1), (95, 0), (84, 0), (60, -1), (0, -2), (45, 0), (49, 0), (89, -2), (33, 1), (14, 1), (65, 0), (44, 0), (52, -1), (20, 1), (77, 0), (28, 1), (96, 1), (77, 1), (42, 1), (9, -1), (97, -1), (93, -1), (86, -2), (93, -1), (51, -2), (37, -2), (64, 1), (3, -2), (43, 1), (61, 1), (31, -1), (87, 0), (0, 0), (85, -1), (25, -2), (68, -1), (36, 0), (51, -1), (32, -2), (35, 0), (84, 0), (33, -1), (80, 1), (65, 1), (91, 1), (87, -2), (41, 1), (73, 1), (50, -2), (91, 1), (78, -1), (35, -1), (2, -2), (71, -1), (94, -2), (81, 1), (74, -2), (48, 1), (18, 1), (96, -1), (84, -2), (64, -2), (83, -1), (94, -2), (49, 0), (97, -2), (5, -2), (59, -1), (57, -2), (10, 1), (43, -2), (95, -2), (92, 0), (13, 0), (76, 0), (98, -1), (90, 1), (71, 0), (73, 1), (95, 1), (50, 0), (75, 1), (54, -2), (99, -1), (2, -2), (45, 1), (91, -1), (54, 0), (94, -2), (82, -1), (44, 1), (49, -1), (87, -1), (42, 1), (50, 1), (61, 1), (74, 0), (58, -2), (8, 1), (21, -2), (82, 0), (50, 0), (0, -2), (61, -1), (48, 0), (0, 0), (25, -1), (37, 0), (43, -2), (98, -1), (53, -1), (19, 0), (61, 1), (1, 1), (20, -1), (16, -1), (99, -2), (55, 1), (6, -1), (25, 1), (24, 0), (76, -1), (55, -2), (14, 0), (90, -1), (5, 1), (65, 0), (6, -1), (15, 1), (33, -2), (35, -1), (5, -2), (94, -2), (5, 0), (31, -1), (29, 1), (90, 1), (84, 1), (64, 1), (55, 0), (77, -1), (12, 1), (55, 1), (33, -1), (5, 1), (42, 1), (71, -1), (69, -2), (91, -2), (53, 1), (93, -1), (99, -1), (93, 0), (24, 1), (24, -1), (40, -2), (62, 0), (48, 1), (31, 1), (12, -1), (66, -1), (10, 0), (30, 1), (4, 1), (10, 1), (8, -2), (54, -1), (97, -1), (51, 1), (68, 1), (93, 0), (13, -1), (2, -1), (66, 1), (12, -1), (39, -1), (63, 0), (82, -2), (8, 1), (68, 1), (70, -2), (39, -1), (17, -1), (28, -2), (50, -2), (27, -1), (96, 1), (54, 1), (13, 0), (58, 0), (23, -2), (30, 0), (25, -2), (49, -1), (99, -2), (63, 0), (30, 0), (96, 1), (77, 1), (15, -2), (10, -2), (99, 1)], 58), ([(39, 0), (65, 0), (17, 0), (53, -1), (46, 1), (31, -1), (47, -1), (97, -1), (89, -2), (2, 1), (30, -1), (86, -2), (18, -2), (48, -2), (60, -2), (71, -2), (34, 1), (66, 1), (54, -2), (75, 0), (33, -1), (3, -1), (29, 0), (51, 0), (22, -1), (68, -1), (71, 0), (49, 1), (35, -1), (65, -1), (63, -1), (54, 1), (85, 0), (89, 1), (35, -2), (60, -1), (83, -1), (17, -2), (73, -1), (25, 0), (67, -2), (28, -2), (28, -2), (82, -1), (28, 0), (37, 0), (11, 0), (61, -1), (75, 1), (59, -1), (38, 1), (90, -2), (54, 0), (0, 0), (13, 1), (21, 1), (91, 1), (47, -2), (51, -2), (23, 0), (42, -1), (69, 1), (84, -1), (58, 1), (4, 0), (63, 1), (94, 1), (52, 0), (96, -2), (47, -1), (99, -1), (1, 1), (72, 0), (8, -2), (0, 1), (83, 1), (82, -1), (93, 1), (16, 0), (22, 0), (31, -2), (84, 1), (60, -1), (59, -2), (60, -2), (35, 0), (81, -1), (62, 0), (81, 1), (13, -1), (2, -2), (54, -1), (83, -2), (8, 0), (64, 0), (10, -1), (60, -2), (76, 1), (19, -1), (96, -1), (53, -1), (93, -1), (56, -1), (63, 0), (15, 0), (77, 0), (5, 0), (84, 0), (14, 1), (91, -1), (9, -1), (20, 0), (25, 1), (63, 1), (48, 0), (39, 1), (85, 1), (11, -1), (46, 0), (58, 1), (72, 1), (70, -2), (28, 1), (58, -1), (8, -1), (7, 0), (25, 1), (98, 0), (90, -2), (11, 1), (53, 1), (70, -2), (55, -1), (68, 1), (57, -2), (4, 0), (58, 0), (77, -2), (16, -1), (42, 0), (35, 0), (66, 1), (57, -1), (13, -1), (69, -1), (62, -1), (78, -1), (82, 1), (71, -2), (16, -2), (14, -2), (72, 1), (51, 1), (85, -1), (76, 1), (83, 1), (85, 1), (8, -1), (3, -1), (60, 0), (90, -2), (78, 0), (35, -2), (10, -2), (11, 1), (42, 0), (23, -2), (80, 1), (71, 1), (34, 0), (47, -1), (75, -2), (42, -1), (56, 1), (82, 1), (18, 0), (29, 0), (5, 0), (69, -1), (83, 1), (26, 1), (74, 1), (74, 1), (63, -1), (7, -1), (68, 0), (94, 0), (16, 1), (42, 1), (35, -2), (98, -2), (79, 0), (72, -2), (6, 0), (52, -2), (33, 0), (22, 1), (48, -1), (55, -2), (98, -2), (60, 0), (87, -2), (86, -1), (99, 1), (42, -1), (80, -2), (32, 0), (51, -2), (29, 1), (11, 1), (59, -1), (30, 0), (49, 1), (29, -2), (96, 1), (14, -2), (87, 0), (26, 0), (49, -1), (23, -2), (79, 1), (16, -2), (5, -1), (86, -1), (94, 0), (93, -1), (98, 1), (78, -2), (86, -2), (64, 0)], 65), ([(39, 1), (51, 1), (16, -1), (97, -2), (59, 1), (74, 1), (56, 1), (87, -1), (4, -1), (64, 1), (35, 1), (26, 0), (24, -2), (65, -1), (29, 0), (13, -1), (4, -1), (80, -2), (34, -1), (95, -2), (28, -1), (71, -1), (3, -2), (17, 1), (68, 1), (12, 0), (29, 1), (69, 1), (0, -2), (63, -1), (70, 1), (29, 0), (38, 0), (40, -1), (99, -2), (57, -2), (16, 0), (25, -1), (57, -2), (40, -1), (35, -1), (65, 1), (49, 0), (60, 0), (96, 1), (59, 1), (17, 0), (43, 1), (6, 1), (34, -1), (84, 0), (36, -2), (25, 1), (18, -2), (31, 0), (7, -2), (42, 0)], 83), ([(28, 1), (11, 0), (93, -1), (40, -2), (87, 1), (34, 0), (68, -2), (84, 1), (78, 1), (39, -1), (1, 1), (30, -2), (37, 1), (85, -1), (3, -2), (14, -1), (75, 0), (42, -1), (85, 0), (45, -1), (64, 1), (84, 1), (25, -1), (31, -1), (4, -1), (41, -1), (64, 0), (13, 1), (84, -1), (17, 0), (69, 0), (94, -2), (28, -2), (97, 0), (36, 0), (10, -2), (5, -1), (54, 1), (9, 0), (39, -2), (11, 1), (22, -2), (62, 1), (60, 0), (83, -2), (49, -1), (35, -2), (6, 0), (48, -1), (49, -1), (45, -1), (8, -1), (65, 0), (75, -1), (41, 1), (14, -1), (36, 1), (92, -2), (90, 0), (34, -2), (37, -1), (42, -1), (85, -2), (83, -2), (62, 0), (65, -2), (48, -2), (24, 1), (15, -2), (4, 0), (62, -1), (47, -1), (98, -2), (83, 0), (86, 0), (10, 0), (75, 0), (21, -1), (16, -1), (43, 0), (5, 0), (49, -1), (93, 1), (67, -1), (51, -1), (21, -2), (27, -2), (53, 1), (90, 1), (75, -1), (41, 0), (14, -2), (5, 0), (62, -2), (35, -1), (87, 0), (1, -2), (71, 0), (19, -1), (42, 0), (71, 1), (53, 1), (12, 0), (52, 1), (66, -1), (69, 1), (84, 1), (0, -1), (43, -1), (81, 0), (14, 1), (39, 1), (58, -1), (92, 0), (42, -2), (71, -2), (56, 0), (93, 1), (91, -2), (21, -1), (19, -2), (75, -1), (25, -2), (40, 1), (94, 0), (69, 1), (54, -2), (29, 1), (74, -2), (93, 1), (56, -1), (4, -2), (81, -2), (10, 0), (53, -2), (54, 1), (82, -1), (95, -2), (59, 1), (9, -1), (12, 0), (13, -2), (23, 1), (39, 1), (75, -2), (46, 1), (55, 1), (96, -1), (23, -2), (61, -2), (5, -2), (36, -1), (40, -1), (5, -1), (3, -2), (22, 0), (17, 1), (10, 1), (3, -2), (65, -1), (58, 1), (13, 0), (71, 0), (4, -1), (45, 0), (50, -1), (62, -2), (67, -1), (81, -2), (38, -2), (49, 0), (23, -1), (54, 1), (88, 1), (69, 0), (20, 0), (87, 1), (23, 1), (49, 1), (18, 1), (46, 1), (51, -2), (59, 0), (42, 0), (16, 0), (71, 0), (95, 1), (28, -2), (30, 0), (46, -1), (82, 1), (4, -2), (61, -1), (41, -1), (92, 1), (83, -2), (75, -2), (48, -2), (8, 0), (86, 0), (1, 1), (14, 0), (29, 0), (40, -2), (96, 1), (41, -1), (16, 0), (89, 0), (97, -2), (79, -1), (86, -1), (57, 1), (89, 0), (36, -1), (56, 1), (34, -1), (8, 0), (35, 0), (66, 0), (78, 1), (31, 1), (47, -2), (66, -2), (20, 0), (24, 0), (64, 1), (21, -1), (89, -1), (97, 1), (19, 1), (5, 0), (84, 0), (89, 0), (64, 0), (97, -1), (72, -1), (81, 0), (43, 1), (63, -2), (68, 0), (69, 1), (89, -2), (10, -1), (32, -2), (13, 1), (42, 1), (33, 1), (20, 0), (96, 0), (57, 1), (35, -2), (18, -2), (12, 0), (60, -2), (76, -2), (96, -1), (65, -1), (82, -1), (87, 0), (71, 1), (37, -1), (5, -2), (63, 0), (76, 1), (30, -2), (77, -1), (68, -1), (25, -1), (0, -2), (37, -1), (31, -2), (75, -2), (69, 0), (70, -1), (54, 0), (10, 0), (70, -2), (96, -2), (58, -1), (74, -1), (39, 0), (34, -1), (52, -2), (40, -2), (64, 1), (52, 0), (69, 1), (24, -1), (41, 0), (91, 0), (19, -1), (42, 0), (98, -2), (54, -1), (38, 0), (22, 0), (98, 1), (37, 1), (25, 0), (98, -2), (0, -1), (21, 0), (43, 0), (48, 1), (56, -2), (16, 1), (36, 1), (90, -2), (22, -2), (65, 0), (46, 1), (26, 0), (68, -1), (72, 0), (74, 0), (80, 0), (4, -1), (30, -1), (37, 0), (98, -1), (2, 1), (69, -1), (20, -1), (43, 1), (63, -1), (32, -2), (86, 0), (73, -2), (32, 0), (45, -2), (50, 0), (7, -2), (87, 1), (53, 0), (3, 1), (91, -1), (1, 0), (84, -2), (21, -1), (68, -2), (68, 1), (23, -1), (66, 1), (1, 0), (68, -1), (60, 0), (57, -2), (9, -1), (80, -2), (28, 1), (81, 0), (27, -1), (87, 1), (27, 0), (53, -2), (11, 1), (10, -1), (75, 0), (71, 0), (66, 1), (40, 0), (67, 1), (41, 0), (64, -1), (94, -1), (54, -2), (73, -2), (86, -1), (1, 1), (56, 0), (66, -2), (88, 1), (69, -2), (89, -2), (35, -1), (98, 0), (18, 0), (42, 1), (59, 0), (62, 1), (76, 1)], 67), ([(82, 1), (8, 1), (43, 0), (39, -1), (45, -1), (61, 0), (69, -1), (65, 1), (66, 1), (25, 0), (83, -2), (49, -1), (67, -2), (56, 1), (64, 0), (8, 0), (47, -2), (27, 1), (81, 0), (17, -2), (10, 1), (58, -1), (47, -1), (40, -2), (28, -2), (7, -1), (10, -1), (14, -2), (25, 0), (5, 0), (92, 0), (20, 1), (15, 0), (22, 1), (8, -2), (37, -1), (60, -2), (8, 1), (63, -2), (75, -1), (7, -1), (5, 1), (92, -2), (19, 1), (61, 0), (44, -2), (13, -2), (50, -1), (64, 1), (64, -2), (40, 1), (20, 0), (53, 0), (66, -1), (51, 0), (29, -1), (58, -1), (17, 0), (47, -1), (87, -1), (0, 1), (84, -1), (27, -1), (57, 1), (51, -2), (46, 0), (66, -2), (81, -2), (25, -2), (93, 1), (7, 0), (60, -1), (21, 0), (24, 1), (98, -2), (6, -1), (70, -1), (37, -1), (83, 0), (4, 0), (11, -2), (63, -1), (50, -2), (65, -1), (73, 1), (53, -2), (36, -2), (18, 1), (16, -2), (71, 0), (43, 1), (62, -2), (45, 1), (68, -1), (60, 0), (71, -2), (20, -2), (85, -2), (26, 1), (56, -2), (86, -1), (91, -1), (66, 0), (37, 1), (91, -2), (19, 0), (55, 0), (8, -2), (2, -1), (26, -1), (55, -2), (94, -2), (0, 0), (4, 1), (86, 0), (38, 0), (57, -2), (35, -2), (4, -2), (71, 1), (27, 1), (80, -1), (8, -2), (84, 0), (5, 0), (11, -1), (79, 1), (59, -1), (59, 1), (67, -1), (85, 1), (77, 1), (0, 1), (86, 0), (40, -1), (81, 1), (72, 1), (44, -2), (48, 1), (31, -2), (14, -1), (92, -1), (35, 0), (33, -1), (40, -1), (80, 1), (77, 0), (35, -1), (56, 0), (9, -1), (2, -2), (78, 1), (31, -1), (15, -1), (11, 1), (80, -1), (95, 1), (98, 0), (59, 1), (24, -2), (57, -2), (33, -2), (90, -1), (90, 0), (14, -1), (6, 0), (54, -1), (10, 1), (11, -2), (25, -2), (79, -2), (20, -2), (18, 0), (18, -2), (57, 1), (53, 1), (41, 0), (92, -2), (20, -2), (84, 0), (24, 1), (43, -2), (72, 1), (56, 0), (7, -1), (72, -2), (50, -2), (79, 0), (62, 0), (20, -1), (49, 1), (52, -1), (39, 0), (21, -1), (78, 1), (31, -2), (36, -1), (91, -1), (30, -1), (7, -1), (40, 1), (36, 0), (90, 1), (16, -2), (81, 0), (63, 1), (55, -1), (45, 1), (52, 1), (88, -1), (5, -2), (65, 1), (94, 1), (27, 1), (79, 1), (77, -2), (85, -1), (41, 1), (29, 0), (40, -2), (72, 0), (25, 1), (15, 1), (77, -2), (10, 1), (59, -2), (23, 0), (29, 1), (36, 1), (81, 0), (59, 0), (35, 0), (63, -2), (76, -2), (87, 1), (17, -1), (39, 1), (64, -2), (21, -2), (56, -1), (41, 1), (36, 0), (20, 0), (97, -2), (62, 1), (19, -2), (74, -1), (65, -2), (30, 0), (55, -1), (54, -2), (64, 1), (66, -1), (7, 0), (3, 0), (43, 0), (72, -2), (27, -2), (31, -1), (67, -1), (59, 1), (93, -1), (3, 1), (66, -1), (25, -1), (72, 1), (13, 0), (74, -1), (33, 0), (4, -1), (7, 1), (59, 1), (73, -2), (60, 1), (90, -2), (86, 0), (21, -2), (36, 1), (18, 1), (86, 1), (16, 1), (1, -1), (60, 0), (74, -2), (63, 0), (31, -2), (46, 1), (34, -2), (50, -1), (21, 1), (34, 1), (0, 1), (25, 1), (99, 1), (22, -2), (67, -1), (48, -2), (51, 1), (43, 0), (73, -1), (36, -2), (84, 0), (54, 0), (30, -1), (89, -2), (37, -2), (15, -2), (30, 1), (67, 0), (9, 0), (53, 1), (33, 1), (93, 1), (89, 1), (8, 1), (5, 1), (86, -1), (46, 0), (6, -2), (27, 0), (5, 1), (44, -2), (88, 0), (75, 1), (54, -1), (10, -2), (35, -1), (22, 1), (0, -1), (75, -1), (52, 0), (56, -2), (40, 0), (10, 0), (77, -1), (54, -1), (77, 0), (78, -2), (1, -1), (28, -2), (30, 0), (34, -2), (81, 0), (91, 1), (15, -2), (58, 1), (36, -2), (73, -1)], 45), ([(35, -2), (92, -2), (61, -2), (34, 1), (79, 0), (4, 0), (83, 0), (71, -1), (52, 1), (72, 1), (83, -1), (88, -1), (59, -2), (19, -1), (42, 1), (4, 1), (29, 0), (97, -2), (7, -1), (75, -2), (28, -2), (0, -1), (53, 1), (3, 1), (80, -1), (11, 0), (29, 1), (83, -2), (54, -2), (2, -1), (5, -2), (44, 0), (93, -2), (38, -2), (17, -1), (1, 0), (70, 1), (41, 1), (82, 0), (64, -2), (70, -2), (66, -1), (25, -2), (14, 0), (36, -2), (51, -2), (35, 1), (11, 0), (69, -2), (40, -2), (37, -1), (2, -1), (56, -1), (60, -1), (89, -2), (79, 0), (96, 1), (46, 1), (99, -2), (54, -2), (65, -1), (41, -2), (7, 1), (21, 1), (21, 1), (18, -2), (7, -1), (31, -1), (68, 1), (72, 0), (67, -2), (86, 0), (8, -1), (94, -1), (98, -1), (9, 0), (33, 0), (86, -1), (44, 1), (48, 0), (71, 1), (25, 0), (98, 0), (43, 0), (87, 0), (76, -2), (78, 0), (51, 1), (28, 1), (47, 1), (24, -2), (6, 1), (17, -1), (63, 1), (72, 1), (12, 0), (35, -2), (25, -2), (23, -1), (6, 1), (18, 0), (95, 0), (16, -2), (90, -1), (2, -1), (79, -1), (29, -1), (49, 1), (41, 1), (7, 0), (57, -2), (75, -1), (10, -1), (27, -2), (87, 1), (92, -1), (46, 1), (48, -1), (30, -1), (74, -1), (57, -2), (45, -1), (53, -2), (94, 1), (98, 1), (4, -1), (91, 0), (39, -1), (36, 0), (56, 1), (7, -1), (66, 1), (69, 1), (52, -1), (7, -2), (57, 0), (96, -1), (92, 0), (47, -2), (80, -2), (77, 1), (18, -2), (43, 1), (77, -1), (1, -2), (49, 0), (14, 0), (82, 1), (37, -1), (77, 0), (5, 1), (35, -2), (39, 1), (28, -1), (92, 0), (45, 1), (42, 1), (79, 1), (79, -1), (69, 1), (88, -1), (85, 1), (96, -1), (53, -1), (13, 0), (60, -1), (15, -1), (30, -2), (64, -2), (11, 1), (36, -1), (76, 1), (10, 1), (34, 1), (19, 0), (79, -1), (69, 0), (76, 1), (7, -2), (67, 1), (63, -2), (32, 0), (90, 1), (21, 0), (86, 0), (61, -1), (23, -2), (52, -2), (39, 0), (85, -2), (15, -2), (3, 0), (59, -2), (50, 0), (80, 1), (11, 1), (82, -1), (3, -1), (72, 0), (38, 0), (76, 1), (2, 0), (61, 1), (2, -1), (99, -2), (37, 1), (84, -2), (49, -1), (18, 1), (33, 1), (98, -1), (77, 1), (81, 1), (72, -2), (21, 1), (94, -1), (77, 1), (0, 0), (40, 0), (3, 1), (43, 0), (61, 1), (30, -1), (93, 0), (28, 0), (92, -1), (3, 1), (15, -2), (70, 0), (32, -1), (32, -2), (82, -2), (39, 1), (27, -2), (19, -1), (0, 1), (89, 0), (46, 0), (74, -1), (43, 1), (43, -2), (60, -1), (32, 1), (48, 1), (57, 0), (4, 1), (60, 0), (76, -1), (44, -1), (64, -2), (84, 1), (70, 0), (31, 1), (45, -1), (16, 0), (72, -1), (65, 1), (58, -2), (53, 0), (68, 1), (54, -1), (15, -2), (16, -2), (85, 1), (85, -2), (10, -2), (89, 1), (33, 0), (39, -1), (25, -1), (47, -1), (59, -2), (26, -1)], 29), ([(83, 1), (42, 0), (80, 0), (64, -2), (14, 0), (79, -1), (70, -1), (93, 0), (93, 1), (40, 1), (93, 1), (75, -2), (92, -1), (87, 1), (40, 0), (32, 1), (56, 1), (59, -1), (58, -2), (70, 1), (75, 0), (13, 1), (35, 1), (94, 0), (1, 0), (95, -2), (45, 0), (71, -1), (18, 1), (45, -1), (20, 0), (72, -2), (14, 0), (97, 0), (29, -2), (65, -2), (21, 1), (0, 0), (76, -2), (83, 0), (70, 0), (25, -1), (72, 1), (52, -2), (51, -1), (40, -1), (1, 0), (50, 0), (82, 0), (75, -1), (85, -1), (28, 0), (32, -1), (67, -2), (99, 1), (31, -1), (79, 1), (63, 0), (95, -2), (46, -1), (2, -2), (35, 0), (87, 0), (11, 0), (57, 1), (98, 1), (5, 0), (27, 1), (27, 1), (90, -1), (37, 1), (24, -1), (57, 0), (43, -2), (2, -2), (70, -2), (26, -2), (65, 1), (46, 0), (89, 1), (34, -2), (45, 0), (56, 1), (33, -2), (35, -2), (55, -2), (16, 0), (93, -2), (67, 1), (56, -2), (70, 0), (75, -2), (50, 1), (71, 0), (93, -2), (83, -2), (15, 0), (26, 1), (75, -1), (60, 0), (1, -2), (39, 0), (52, 1), (59, 0), (6, 0), (42, -2), (67, -1), (85, -1), (76, 0), (75, 0), (60, 1), (27, -1), (42, -1), (6, 0), (5, -2), (72, 0), (53, -1), (61, -2), (19, -1), (52, -1), (13, 0), (1, 1), (54, -2), (93, 0), (85, -1), (77, 0), (13, 0), (68, -1), (49, 1), (68, -2), (99, 0), (43, 0), (46, -2), (5, -1), (58, 1), (58, 0), (24, -1), (41, -1), (28, -2), (25, -2), (1, -1), (70, -1), (49, -2), (54, 1), (53, -2), (64, -2), (18, -2), (80, -1), (43, -1), (5, -2), (20, 1), (14, 1), (8, 0), (54, -2), (86, -2), (39, -2), (72, -1), (1, 1), (77, -2), (49, -2), (21, -2), (79, 1), (88, -2), (95, 1), (72, 0), (43, 0), (50, 0), (65, 1), (40, -1), (62, 0), (5, 0), (3, 0), (26, 1), (11, -1), (35, -2), (43, 1), (19, 0), (20, -1), (98, -1), (70, -2), (90, 1), (63, 0), (45, 0), (92, 1), (71, 1), (59, -2), (34, -1), (13, 0), (23, 0), (7, 1), (37, -1), (2, -1), (10, 1), (13, -2), (89, 1), (16, 0), (9, 1), (19, -1), (60, -2), (43, 1), (99, 1), (36, -2), (56, 1), (21, 0), (99, 1), (49, 0), (92, 0), (55, -2), (47, -1), (74, -1), (40, -2), (14, 0), (93, 0), (87, -2), (43, 1), (43, 0), (79, 1), (61, -2), (26, -1), (79, 0), (32, -2), (27, -1), (94, -1), (85, 0), (12, -2), (61, 0), (61, 0), (22, 1), (24, -2), (6, 1), (60, -1), (27, -2), (25, 1), (66, 0), (72, -1), (49, 1), (64, 0), (99, 0), (94, 1), (17, 1), (38, 1), (72, -1), (6, -2), (29, 0), (55, -2), (71, -1), (40, -2), (98, 1), (61, 1), (45, -2), (37, -1), (88, -2), (94, 1), (73, -1), (54, 0), (98, 0), (47, 1), (24, -1), (43, 0), (33, 1), (35, -1), (8, -2), (89, 0), (89, 1), (12, 1), (68, 0), (68, 1), (43, -1), (21, 1), (86, -2), (1, -1), (58, -2), (88, -2), (16, -1), (84, -1), (21, -2), (91, 0), (42, 1), (86, 0), (62, -2), (7, -2), (45, -2), (60, 0), (72, -1), (8, 0), (93, -1), (78, -2), (78, -1), (65, 0), (27, 1), (57, 0), (71, -1), (82, -1), (10, -1), (96, 0), (94, 0), (42, 1), (64, -2), (86, -1), (25, -1), (9, 0), (45, -2), (14, -2), (49, 0), (25, -2), (93, 1), (37, 0), (28, 0), (94, -2), (39, -1), (38, -1), (42, 0), (69, 1), (79, -1), (70, -1), (21, -1), (22, 0), (1, -2), (5, 1), (20, 1), (7, 1), (47, 1), (61, -2), (73, 0), (66, 1), (27, -1), (48, -2), (86, -2), (55, -2), (44, 1), (99, -2), (3, 0), (27, 0), (12, -1), (27, -1), (14, 0), (39, -1), (93, 0), (45, -2), (3, 0), (0, -1), (84, 0), (41, -1), (6, 1), (2, -1), (92, 1), (78, -1), (32, 0), (96, 0), (87, -1), (48, 1), (51, 0), (90, -1), (33, -2), (37, -1), (47, -1), (34, -1), (16, 0), (92, 1), (55, -1), (2, -2), (40, -2), (43, 1), (71, -1), (49, 0), (23, 0), (5, 1), (21, 1), (67, -2), (98, 0), (5, 1), (29, -2), (14, -1), (1, 1), (45, 0), (48, -2), (91, -1), (90, -1), (35, -1), (8, -2), (71, -1), (20, -2), (88, -2), (45, -1), (76, -2), (37, 1), (89, 0), (39, -1), (82, -1), (95, 0), (75, 0), (11, -2), (82, 1), (42, 1), (42, 1), (10, -2), (27, 0), (54, 1), (82, 1), (81, -1), (89, 1), (61, 0), (64, 1), (89, 1), (24, 0), (27, -1), (93, -1), (62, -1), (71, 0), (82, 0), (81, -2), (75, 1), (10, -2), (93, -2), (16, 1), (31, -1), (53, 1), (90, 0), (52, 0), (46, 1), (64, -1), (20, 0), (29, 1), (5, -2), (25, 1), (0, 1), (38, 0), (98, -2), (11, 0), (46, 0), (7, 0), (4, 1), (97, -2), (16, 1), (73, 0), (70, 0), (3, 0), (22, 1), (64, 0), (29, 0), (78, 0), (66, -1), (72, 1), (22, -1)], 26), ([(16, -2), (62, 0), (82, -1), (6, -2), (16, 1), (27, 0), (86, -2), (11, 0), (12, 1), (84, -2), (87, -2), (70, 1), (30, -1), (64, 0), (90, -1), (26, 0), (15, -1), (91, -1), (54, 0), (11, 0), (79, 1), (98, -2), (82, -2), (23, -1), (62, -2), (93, 1), (54, -2), (57, -2), (26, 1), (86, 0), (56, -2), (69, -1), (56, 0), (87, -2), (55, -2), (1, 0), (94, -2), (18, 0), (14, -2), (97, -2), (69, 1), (9, -2), (14, 1), (30, -2), (81, 0), (91, 0), (34, -1), (31, -1), (7, 0), (69, -1), (69, -1), (63, 0), (86, 1), (46, 1), (90, -1), (62, -2), (9, -1), (34, 0), (48, 0), (64, 1), (88, -1), (56, 1), (42, -2), (22, 0), (71, 1), (23, 0), (84, 0), (15, -2), (1, 1), (36, -1), (73, -1), (38, 1), (1, 0), (68, 1), (74, -1), (21, -2), (55, 1), (88, -1), (27, 0), (11, 1), (0, 0), (78, 0), (23, 0), (11, 1), (97, -1), (84, -2), (40, 1), (34, -2), (29, 1), (86, 0), (37, 0), (71, -2), (30, -1), (63, 1), (19, -2), (23, -2), (58, 1), (45, -2), (99, -1), (68, -2), (14, -2), (63, 1), (57, 1), (17, -2), (99, 0), (68, -1), (30, -2), (68, -2), (56, 0), (96, 0), (76, -2), (4, 0), (8, -2), (51, -2), (87, -1), (78, 1), (16, -2), (57, 0), (37, 0), (60, 1), (6, 0), (60, 0), (64, -2), (87, 1), (78, -2), (87, -1), (29, -2), (85, 0), (78, 0), (76, 1), (70, 0), (43, -2), (99, 1), (20, -1), (51, 0), (18, -1), (58, -1), (91, -1), (52, -1), (1, 1), (52, -1), (12, -2), (36, -1), (47, -1), (94, 1), (43, -1), (7, 1), (31, -1), (34, 0), (49, -1), (81, 0), (54, -2), (22, -2), (41, -2), (31, -2), (59, 0), (87, 1), (96, 1), (1, 1), (71, -2), (84, -2), (97, -2), (16, -2), (69, -2), (11, -2), (90, 0), (9, 1), (14, 0), (78, 1), (33, -1), (32, -2), (82, -2), (93, 0), (35, -1), (16, 1), (97, 1), (52, -1), (42, 1), (40, -1), (79, 1), (74, 1), (7, -2), (97, -2), (16, 1), (97, 0), (19, -2), (46, 1), (43, 0), (2, -1), (17, 0), (87, -1), (10, 0), (57, 1), (98, -1), (34, -1), (13, 1), (91, 1), (79, 0), (1, 1), (24, -1), (89, -2), (87, 0), (83, 0), (20, -2), (44, 1), (80, 0), (15, 1), (4, -2), (75, 1), (21, -1), (51, 1), (4, -2), (37, -2), (81, -1), (98, 0), (29, 0), (31, -2), (61, -1), (6, -2), (34, -1), (46, -1), (30, 0), (2, -2), (22, -2), (65, -1), (56, -1), (32, 1), (16, -2), (38, 0), (13, 1), (49, 0), (85, -2), (68, -1), (92, 1), (40, -2), (71, -2), (82, 0), (43, -1), (73, 1), (72, -2), (48, 0), (77, 0), (78, -1), (52, 1), (11, -2), (82, -1), (12, -2), (72, 1), (62, -1), (59, 0), (64, 1), (30, 1), (7, -2), (24, -2), (74, -2), (59, -2), (77, -1), (46, -2), (28, 0), (15, 1), (49, -1), (54, 1), (84, 0), (56, -2), (25, -2), (75, -1), (35, 0), (31, 0), (15, 1), (81, 0), (34, 0), (2, 0), (22, 0), (19, -2), (19, 0), (65, -2), (35, -1), (80, 1), (6, 0), (17, -1), (12, 1), (38, -1), (37, 1), (8, 0), (89, -2), (88, 0), (53, 0), (78, -2), (22, 1), (10, -2), (98, 0), (81, 0), (31, -1), (37, -1), (70, 0), (54, -1), (79, 0), (15, -2), (61, 0), (2, -2), (17, -2), (24, 1), (48, 1), (78, 1), (47, 1), (6, 0), (99, 1), (2, 0), (66, 0), (53, 0), (63, -2), (66, 1), (91, 1), (41, -1), (72, 0), (99, -1), (9, -2), (3, -2), (20, -2), (38, 1), (22, 1), (52, 0), (89, 0), (42, -1), (13, -1), (14, -2), (26, 0), (27, 0), (48, 0), (56, 0), (18, 0), (92, 1), (79, -1), (16, 0), (19, 0), (70, -1), (69, 0), (79, -1), (23, -2), (45, -1), (56, -2), (88, 1), (68, -2), (13, -1), (30, -1), (54, 1), (4, 1), (0, -1), (63, -2), (78, 0), (91, -2), (0, -2), (22, -1), (67, -1), (36, 1), (18, 1), (56, -2), (66, -1), (90, -2), (82, -2), (94, -2), (71, -2), (99, -1), (9, 1), (56, -1), (26, 1), (37, -1), (68, 1), (36, 1), (77, -1), (80, -1), (10, -2), (52, -2), (8, -2), (18, 0), (72, 1), (86, 1), (73, -2), (8, 1), (23, 0), (58, -2), (55, -2), (32, -2), (58, -1), (90, -1), (45, 0), (92, 1), (1, 1), (28, 1), (0, -2), (18, -1), (50, -1), (59, 1), (47, 0), (22, 1), (69, -2), (19, 1), (27, 1), (31, -1), (82, -2), (55, -2), (85, -2), (3, -2), (24, -1), (77, -1), (48, -2), (63, 0), (88, -1), (15, 1), (34, -2), (35, 0), (62, -2), (0, -1), (51, -2), (87, -1), (8, 1), (69, 0), (43, -2), (38, 1), (74, -1), (61, -1), (20, -1), (2, 0), (66, 1), (60, 1), (6, -2), (28, 1), (89, 0), (24, 1), (76, -1), (84, 1), (49, -2), (30, -1), (69, 1), (65, -2), (95, -2), (83, -1), (46, -1), (34, -1), (61, 1), (56, -1), (10, 0), (98, -2), (24, 0), (78, -1), (40, 0), (7, 1), (69, 1), (4, -1), (72, 1), (8, -1), (5, -2), (55, 0), (20, 1), (14, 1), (64, -2), (19, -2), (22, -1), (7, 1), (34, -1), (60, -2), (21, -2), (67, 1), (7, 0), (6, -1), (67, 1), (91, 1), (1, 1), (61, 0), (8, 0), (63, -2), (99, -1), (24, 1), (46, 1), (46, 1), (60, -2), (28, -1), (2, -2), (26, 0), (94, -2), (27, -1), (21, -1), (11, -2), (2, -1), (95, -2), (48, -2), (78, 1), (12, 0), (30, 0), (80, 1), (85, 1), (43, 0), (33, -1), (12, 1), (59, -1), (74, -2), (14, 0)], 19), ([(47, 0), (63, -2), (60, -1), (35, -1), (14, 0), (15, -1), (95, 1), (88, -2), (97, 0), (75, -2), (58, 0), (77, -1), (92, -1), (71, 1), (72, -1), (96, -2), (99, 1), (87, -2), (12, -2), (61, 0), (45, 1), (88, 1), (22, 1), (21, -2), (14, -2), (54, 0), (65, -1), (9, -2), (34, 0), (86, 0), (1, -2), (15, -2), (9, 0), (49, 1), (9, 0), (53, -2), (36, -1), (63, 0), (71, 0), (39, -2), (28, 0), (56, 0), (91, -2), (51, -2), (10, 0), (43, 0), (95, 0), (16, 1), (59, 0), (21, 0), (95, -1), (44, 1), (91, 0), (29, -1), (0, 1), (46, -2), (90, -1), (24, 1), (67, -1), (94, 1), (34, -2), (11, -2), (71, 1), (2, 0), (68, -1), (99, 0), (20, 1), (80, 1), (66, -1), (56, 0), (8, 0), (39, -2), (45, -2), (25, 0), (61, -1), (26, -1), (87, 0), (63, 1), (95, 0), (31, 1), (89, 0), (37, -1), (93, 0), (70, -2), (26, 0), (23, -2), (83, 0), (18, -2), (82, 0), (77, -2), (28, -1), (71, 1), (43, 1), (18, 1), (67, 0), (71, 0), (72, -2), (79, 1), (92, -2), (59, -1), (5, -1), (0, -1), (26, 1), (28, 0), (48, 1), (64, -1), (13, 0), (48, -2), (36, -1), (31, 1), (33, -2), (47, 0), (30, -2), (79, 0), (36, 0), (75, 0), (95, 0), (31, 1), (66, 0), (37, -1), (28, -2), (83, -1), (49, -2), (62, -1), (87, 0), (89, 0), (50, 0), (11, 0), (17, 1), (99, -1), (93, 1), (19, -1), (21, -2), (98, -2), (35, -1), (89, 1), (25, -1), (3, -1), (16, 1), (26, -1), (83, -2), (92, 0), (8, 0), (20, -2), (65, 0), (82, -1), (38, -1), (72, 1), (96, 1), (27, -2), (86, 0), (51, -2), (80, 0), (92, 0), (0, -1), (97, -1), (59, 1), (77, -2), (44, 0), (99, 1), (16, 0), (8, 0), (0, -2), (53, -2), (67, -2), (29, -2), (69, 1), (94, -1), (62, 1), (68, -1), (78, 0), (5, 0), (88, 1), (35, -2), (47, 0), (35, 0), (27, -2), (84, -2), (58, 0), (74, 0), (90, 0), (61, 0), (59, 1), (94, 1), (77, 1), (96, 1), (75, 1), (82, 1), (78, 1), (87, 1), (17, 0), (22, -1), (16, -1), (24, 1), (96, 0), (23, -1), (26, -1), (70, 0), (63, -1), (94, -2), (15, 1), (56, -1), (13, 0), (35, -2), (7, 1), (85, -2), (32, 0), (53, 1), (42, -1), (70, -1), (32, 0), (41, 1), (14, -2), (2, 1), (5, 0), (50, -1)], 53), ([(8, -1), (95, 0), (28, 1), (12, -1), (74, 1), (0, 1), (22, -2), (84, 0), (49, 0), (29, -2), (61, -1), (46, -1), (5, -1), (11, -1), (51, -1), (30, 0), (10, -1), (72, 0), (68, 1), (49, -2), (7, 1), (63, 0), (66, 1), (84, -1), (59, -2), (23, 0), (19, -2), (77, -1), (50, 1), (97, -2), (98, 1), (76, 0), (41, 1), (50, -2), (2, -2), (82, -2), (45, 0), (52, 0), (28, -2), (22, 1), (94, -1), (56, -1), (46, -2), (66, -2), (18, 1), (29, 0), (37, 1), (99, 0), (93, 0), (81, -2), (80, 0), (60, -1), (39, -1), (1, 1), (3, 0), (92, -2), (54, 0), (1, 0), (77, -2), (29, -2), (91, 0), (21, -2), (94, 1), (42, -2), (72, -2), (18, -2), (79, 0), (0, -2), (73, 1), (60, 0), (66, 0), (90, 0), (18, -2), (88, 1), (29, 0), (10, 1), (3, -1), (15, -1), (50, 0), (45, 1), (97, -1), (65, 1), (10, -2), (8, -2), (4, 1), (46, -2), (33, -2), (49, 0), (12, -1), (17, 0), (12, -1), (14, 1), (77, 1), (54, 0), (84, 1), (84, 1), (81, -1), (29, -1), (76, 0), (10, 1), (26, -1), (94, -2), (8, -2), (35, 0), (61, 0), (87, 1), (83, 1), (77, 1), (47, -1), (80, 1), (21, -2), (13, 0), (86, -1), (95, -2), (80, -1), (26, 0), (80, 0), (3, 1), (11, -2), (86, -2), (97, -1), (24, -2), (13, 0), (31, 1), (52, 1), (98, -2), (88, 0), (34, -2), (69, -1), (40, 0), (2, -2), (70, -1), (24, 0), (30, -2), (75, 0), (34, -2), (46, -2), (62, -2), (18, 0), (77, -1), (11, 0), (16, 1), (36, -1), (80, 0), (68, 1), (78, 0), (92, -2), (93, 0), (35, -1), (24, -1), (9, 1), (41, 0), (86, -1), (91, -1), (31, -2), (52, 1), (31, 1), (14, -1), (49, 0), (38, -2), (99, -2), (7, -2), (63, 0), (42, 1), (97, -1), (27, -2), (10, -1), (59, -2), (90, -1), (26, -1), (51, 1), (51, 1), (11, 1), (51, -1), (45, -2), (41, 1), (75, -2), (76, -2), (52, 0), (16, -2), (95, 1), (79, -1), (72, 1), (91, 1), (34, 0), (95, -1), (84, -1), (87, -2), (29, 1), (14, -2), (96, 1), (3, 1), (68, -2), (1, -1), (29, -2), (58, -1), (22, -1), (0, 0), (57, -2), (84, -2), (92, -1), (15, -2), (58, -1), (44, 1), (20, -2), (9, -2), (85, 0), (57, 0), (33, 1), (11, 1), (99, -2), (75, -1), (9, -2), (51, 1), (93, 1), (68, -2), (37, 0), (15, 1), (90, 0), (85, -1), (48, -1), (34, 1), (6, -2), (83, -1), (10, -2), (60, 1), (11, 1), (56, -1), (95, 1), (97, -1), (50, 0), (74, -2), (89, -1), (1, -2), (57, 1), (20, -2), (10, 1), (2, 0), (0, 0), (83, 0), (43, 0), (45, -2), (23, -2), (93, 1), (85, 1), (40, 0), (27, -1), (86, 1), (80, 0), (56, 1), (23, 1), (28, -2), (32, -2), (5, -2), (16, 0), (4, -2), (61, -2), (69, -2), (86, -2), (42, 1), (89, 0), (80, -2), (73, -2), (65, 0), (76, 1), (73, 0), (80, -1), (24, 1), (69, -2), (67, 1), (84, -2), (67, -2), (8, -1), (69, -1), (51, 0), (1, -1), (96, 1), (85, 1), (1, -2), (65, 0), (72, -2), (96, -1), (78, 0), (71, 1), (63, -2), (67, -2), (55, 0)], 97), ([(14, -2), (51, -2), (8, 0), (17, 1), (28, 1), (64, -1), (50, -2), (59, -1), (12, -2), (6, 0), (2, 1), (39, 1), (26, 0), (25, 1), (75, -2), (56, 1), (50, 1), (26, -1), (35, -2), (1, 1), (28, -2), (5, 1), (12, 1), (71, 0), (48, 1), (23, 0), (25, -2), (10, 1), (60, 0), (98, -1), (5, -2), (1, 1), (14, 1), (67, -2), (92, -1), (75, 1), (16, -1), (10, -2), (41, -2), (72, -2), (1, 0), (87, -1), (3, -2), (18, -2), (69, 1), (91, 0), (48, -1), (57, 1), (4, 0), (85, -2), (82, 1), (93, 1), (22, -1), (37, 0), (2, 1), (2, -1), (2, 0), (26, 1), (56, 0), (69, 1), (72, 0), (93, -1), (49, -1), (81, -1), (75, -2), (43, -2), (69, -2), (79, 0), (67, -2), (52, -1), (29, -2), (77, 0), (0, -1), (24, -1), (16, -1), (39, 1), (68, 0), (44, -1), (31, -2), (37, 0), (55, -2), (15, -2), (26, -1), (80, 0), (94, -1), (58, 1), (96, -2), (74, -2), (52, -2), (4, 1), (31, -2), (73, -1), (59, -2), (73, 0), (98, -1), (46, 1), (7, 1), (3, 0), (64, 1), (5, 1), (13, 0), (43, -2), (12, 0), (26, -2), (18, 1), (18, 1), (57, -1), (7, -2), (83, 1), (82, 0), (46, 1), (62, -2), (5, -2), (76, 0), (17, -2), (28, -2), (45, 1), (34, -1), (81, -1), (4, -1), (69, -1), (10, -1), (42, -2), (27, -2), (42, 0), (69, -1), (92, -1), (57, 0), (79, -2), (23, 0), (4, 0), (88, -1), (44, -1), (56, -2), (23, 1), (97, 0), (9, -2), (8, -2), (46, 0), (41, 0), (2, 0), (81, 0), (51, -1), (18, -2), (65, 1), (20, 0), (15, 1), (86, 1), (34, 0), (73, 1), (6, 0), (59, 1), (25, 0), (94, 0), (96, 1), (99, -2), (67, -2), (64, 0), (73, 0), (7, -2), (23, 0), (39, -1), (13, -2), (28, 0), (13, -1), (44, 0), (95, 1), (60, -2), (30, 1), (62, -1), (15, 0), (21, -2), (25, 0), (20, 0), (31, 1), (53, -2), (93, -2), (65, 0), (81, -2), (77, 1), (21, -1), (36, 1), (81, 1), (1, -2), (86, -2), (41, 1), (76, 0), (87, 1), (29, -1), (40, 0), (14, 1), (9, 1), (29, -2), (99, 0), (96, -2), (84, -1), (66, 0), (21, 1), (30, -2), (44, -2), (44, 0), (94, 1), (6, 1), (27, -2), (11, 1), (90, -2), (64, -2), (12, -1), (69, -2), (52, 1), (34, 1), (10, -1), (30, 0), (6, 1), (51, 1), (79, 0), (97, -1), (83, 0), (98, -1), (69, 1), (25, -2), (27, 1), (58, 1), (90, -1), (18, 0), (27, 0), (26, -1), (3, 0), (80, -2), (73, 1), (38, -1), (49, 0), (42, 1), (24, -2), (42, 1), (47, 0), (55, 1), (61, 0), (38, 1), (66, -2), (33, 0), (53, 1), (33, 1), (71, 0), (10, 1), (99, 1), (69, 0), (96, -2), (33, 0), (39, -2), (96, 0), (86, 0), (67, -2), (38, 0), (38, -2), (23, 0), (98, -2), (81, -1), (94, -1), (16, -2), (92, 1), (39, -2), (71, 1), (92, 1), (98, 1), (52, 1), (65, 0), (92, 1), (72, -2), (52, -2), (83, 0), (56, 0), (35, -2), (72, -1), (9, -1), (91, 1), (58, 0), (95, 0), (86, -2), (22, -1), (41, -2), (44, -1), (81, -2), (36, -1), (82, -2), (6, -1), (21, 0), (48, -2), (55, 1), (63, 1), (32, 0), (72, -2), (11, 0), (93, 0), (82, 1), (41, -2), (98, 1), (68, -2), (4, 0), (83, 1), (95, -2), (66, 1), (75, -2), (78, 0), (70, 0), (33, 0), (27, -2), (90, -1), (48, 0), (33, -1), (83, 0), (17, 1), (1, 1), (22, 0), (20, -1), (10, -2), (74, -2), (52, 1), (20, 1), (57, 1), (76, -1), (44, -1), (20, 1), (18, -1), (54, -2)], 51)], seed = 7153555591384072550
cc 67712c05513fd5a3b5849d73b27850fd23f5872dca719ae0d83181bb820f1d0e # shrinks to batches = [([((95, 0), -1), ((66, 2), 1), ((70, 3), -2), ((5, 4), -2), ((64, 1), -1), ((20, 4), -2), ((1, 3), -2), ((35, 4), 1), ((48, 0), -1), ((83, 2), -1), ((53, 4), 1), ((9, 3), -2), ((18, 4), 1), ((91, 3), 1), ((81, 1), 1), ((28, 4), 1), ((20, 3), 1), ((25, 0), -2), ((92, 0), -1), ((44, 2), 1), ((34, 4), -2), ((76, 4), 0), ((53, 3), 1), ((56, 2), -2), ((61, 0), 1), ((25, 4), 0), ((59, 4), 0), ((49, 1), -2), ((46, 1), -2), ((14, 2), -1), ((63, 0), 0), ((91, 3), -2), ((42, 2), -2), ((91, 1), 1), ((24, 3), 1), ((32, 1), 0), ((24, 3), 0), ((40, 0), 0), ((89, 3), 0), ((19, 1), -1), ((38, 2), 0), ((55, 2), -1), ((11, 2), 0), ((7, 4), -2), ((65, 3), -2), ((51, 3), -1), ((80, 3), 1), ((94, 3), -2), ((10, 3), -1), ((4, 2), 0), ((25, 3), -1), ((35, 3), 0), ((42, 2), 1), ((75, 4), -2), ((61, 3), 1), ((32, 3), -2), ((12, 1), 0), ((51, 2), -1), ((74, 0), 1)], 47, 4), ([((52, 3), -2), ((1, 0), -1), ((16, 0), -2), ((67, 3), 1), ((23, 1), 0), ((92, 2), 0), ((5, 3), 1), ((44, 3), -1), ((58, 3), -1), ((99, 4), 0), ((8, 3), 0), ((54, 2), -1), ((45, 2), -2), ((79, 1), -1), ((95, 0), 1), ((84, 4), -1), ((2, 1), -2), ((79, 2), 1), ((97, 3), 1), ((95, 1), -1), ((36, 3), 1), ((24, 4), 0), ((4, 3), -1), ((76, 0), 0), ((36, 4), -2), ((15, 1), -2), ((18, 4), -2), ((22, 1), -2), ((61, 2), -1), ((50, 3), 0), ((22, 0), -1), ((27, 2), 1), ((65, 1), 0), ((18, 3), -1), ((60, 0), -2), ((99, 2), -1), ((3, 1), -2), ((72, 3), 1), ((77, 3), -2), ((47, 1), -1), ((19, 3), -2), ((46, 3), -2), ((38, 4), -2), ((6, 4), -2), ((0, 2), 0), ((88, 2), -2), ((64, 3), 1), ((34, 1), -1), ((47, 0), -2)], 57, 1), ([((48, 3), 1), ((70, 0), 0), ((66, 4), 1), ((10, 4), 0), ((76, 3), -1), ((21, 2), 0), ((45, 3), 1), ((27, 4), 0), ((24, 4), 0), ((53, 2), 0), ((59, 1), -1), ((82, 3), 1), ((6, 2), -1), ((46, 3), 0), ((0, 2), 0), ((14, 1), 0), ((82, 2), -1), ((49, 3), -1)], 41, 3), ([((29, 2), -2), ((92, 3), 1), ((91, 4), -1), ((73, 2), 0), ((21, 4), 1), ((67, 1), -1), ((58, 2), -2), ((22, 0), 1), ((59, 4), 1), ((95, 2), 1), ((73, 0), 1), ((30, 4), 1), ((26, 3), 0), ((22, 3), 1), ((18, 2), 1), ((54, 2), 1), ((9, 1), 1), ((43, 4), -2), ((40, 4), -1), ((76, 3), 0), ((12, 1), -1), ((36, 3), -2), ((39, 3), 0), ((45, 2), -2), ((36, 0), 0), ((73, 3), -1), ((57, 2), -2), ((11, 3), -1), ((61, 4), 0), ((28, 3), 0), ((27, 3), -2), ((49, 4), 1), ((98, 4), -2), ((78, 2), -1), ((84, 3), -2), ((2, 2), -2), ((43, 0), 0), ((54, 1), 0), ((93, 3), -2), ((11, 3), 0), ((99, 0), 1), ((81, 3), 1), ((58, 2), 0), ((99, 0), 0), ((63, 2), 0), ((89, 0), -1), ((19, 1), -2), ((72, 3), -1), ((74, 1), 0), ((55, 2), 1), ((98, 4), -1), ((43, 3), 0), ((97, 1), -1), ((96, 2), -2), ((86, 1), 1), ((6, 2), 1), ((89, 1), -2), ((52, 4), -1), ((96, 3), 0), ((80, 1), -1), ((79, 3), -1), ((47, 3), -2), ((17, 0), -1), ((6, 3), -1), ((77, 1), 0), ((1, 4), 0), ((14, 0), -2), ((1, 2), -1), ((71, 4), -1), ((43, 1), 1), ((56, 0), 0), ((63, 2), 0), ((99, 1), 0), ((25, 0), -1), ((99, 0), -1), ((84, 2), 1), ((15, 1), 1), ((50, 1), -1), ((79, 3), 1), ((75, 0), 0), ((55, 0), -1), ((59, 1), 0), ((95, 0), 0), ((78, 2), -1), ((7, 4), -1), ((44, 2), -1), ((53, 2), -2), ((32, 3), -1), ((65, 0), -2), ((14, 0), -2), ((52, 3), 0), ((52, 2), 0), ((93, 1), -2), ((43, 3), -1), ((65, 0), 0), ((35, 2), 0), ((87, 3), 1), ((25, 2), 1), ((88, 1), 0), ((22, 0), -1), ((30, 2), -1), ((90, 0), -1), ((88, 3), -1), ((83, 4), -2), ((40, 1), -1), ((66, 1), 0), ((99, 2), 0), ((10, 1), -2), ((82, 2), -1), ((50, 4), -1), ((60, 2), -1), ((42, 4), -2), ((2, 2), 1), ((67, 4), 0), ((95, 1), -1), ((69, 4), -1), ((8, 4), -2), ((31, 3), -1), ((1, 2), -1), ((71, 1), -1), ((54, 4), -2), ((4, 1), 0), ((78, 3), -2), ((98, 4), 1), ((33, 0), 0), ((32, 2), 1), ((57, 1), -1), ((74, 0), -1), ((18, 4), 1), ((59, 1), 0), ((69, 0), 1), ((65, 1), -2), ((27, 2), 1), ((60, 4), -2), ((13, 0), 1), ((90, 3), 1), ((14, 4), -2), ((28, 1), -2), ((71, 3), -1), ((22, 2), 0), ((83, 3), 1), ((29, 4), 1), ((63, 1), -2), ((10, 4), 0), ((80, 4), 1), ((21, 4), -1), ((53, 1), -1), ((31, 3), 1), ((57, 2), 0), ((28, 1), 1), ((76, 0), 0), ((83, 2), 0), ((9, 2), -1), ((97, 1), 0), ((12, 2), 1), ((51, 4), -2), ((52, 2), 0), ((95, 1), -1), ((84, 2), -2), ((52, 2), -1), ((23, 0), 1), ((16, 0), -2), ((16, 3), 0), ((81, 1), -2), ((79, 1), 1), ((64, 2), 0), ((36, 4), -2), ((60, 1), -2), ((18, 0), 0), ((31, 3), 0), ((87, 4), -2), ((48, 4), -2), ((31, 2), 1), ((94, 0), 0), ((30, 2), -2), ((62, 2), -2), ((11, 3), 0), ((54, 4), 1), ((98, 4), 0), ((2, 3), -1), ((81, 2), -1), ((59, 0), 1), ((93, 2), -1), ((17, 0), 1), ((85, 4), -1), ((31, 0), 1), ((1, 2), 1), ((12, 0), 1), ((98, 3), 1), ((37, 0), -1)], 97, 3), ([((45, 3), 1), ((75, 0), -2), ((6, 0), -2), ((53, 0), -2), ((72, 3), 1), ((33, 1), 0), ((79, 1), 0), ((30, 2), 1), ((9, 0), 1), ((85, 0), 1), ((4, 1), -1), ((34, 4), 1), ((9, 2), 0), ((9, 2), 0), ((66, 3), -2), ((48, 0), 1), ((99, 2), -2), ((87, 2), -1), ((70, 3), 1), ((91, 0), -1), ((9, 3), -1), ((14, 3), 0), ((81, 0), 1), ((10, 4), 0), ((20, 2), -1), ((63, 3), -1), ((99, 1), -1), ((15, 4), -1), ((48, 1), -1), ((60, 1), 0), ((20, 2), 0), ((59, 2), -2), ((97, 3), -2), ((2, 2), -1), ((88, 3), -1), ((45, 2), -2), ((38, 3), 1), ((48, 2), -1), ((88, 1), -1), ((83, 2), -1), ((44, 1), -1), ((11, 0), 1), ((2, 2), -1), ((59, 0), 1), ((27, 1), -2), ((24, 4), -2), ((90, 0), 1), ((73, 0), -2), ((55, 3), 0), ((38, 3), -2), ((44, 2), -2), ((60, 3), -2), ((4, 4), 0), ((61, 3), -1), ((80, 1), -2), ((11, 4), -1), ((43, 1), -1), ((97, 4), 0), ((58, 3), 0), ((94, 3), 0), ((64, 1), 1), ((21, 2), -2), ((11, 4), -1), ((8, 4), 1), ((50, 2), -1), ((38, 1), 0), ((32, 3), -2), ((30, 4), 0), ((61, 4), 0), ((14, 0), 0), ((94, 2), -2), ((65, 4), 1), ((75, 0), 0), ((27, 1), -1), ((13, 0), -2), ((90, 3), 1), ((27, 4), -2), ((63, 3), -1), ((11, 1), -2), ((42, 2), 0), ((59, 0), 0), ((91, 3), -1), ((18, 1), -1), ((47, 2), -2), ((65, 4), -2), ((23, 4), -2), ((41, 1), 0), ((27, 2), 1), ((3, 0), 1)], 70, 0), ([((23, 4), 0), ((86, 4), -1), ((9, 0), -2), ((97, 1), 1), ((42, 2), -2), ((77, 2), 1), ((1, 0), 1), ((96, 3), -2), ((19, 4), -1), ((0, 3), -1), ((22, 1), 1), ((49, 3), 0), ((42, 4), 1), ((63, 0), 1), ((69, 3), -2), ((78, 4), 1), ((73, 4), -1), ((46, 1), 1), ((72, 4), 0), ((57, 4), 1), ((77, 3), -1), ((67, 3), 1), ((50, 2), -2), ((22, 1), 0), ((51, 3), 0), ((89, 3), -1), ((35, 1), 1), ((83, 1), -1), ((13, 2), 1), ((25, 4), 0), ((77, 0), 1), ((83, 2), 1), ((69, 0), 0), ((49, 0), 0), ((30, 3), -2), ((35, 1), 0), ((49, 3), -1), ((78, 2), -2), ((30, 1), -2), ((68, 3), 0)], 85, 3), ([((2, 1), -1), ((35, 4), -1), ((86, 3), -1), ((19, 4), -1), ((60, 0), -1), ((92, 0), -2), ((90, 2), -2), ((46, 2), 0), ((28, 3), 1), ((52, 2), 1), ((7, 3), -1), ((65, 0), 0), ((76, 4), 1), ((66, 2), 0), ((21, 1), -1), ((90, 2), -1), ((43, 2), 0), ((27, 1), 0), ((75, 3), -2), ((82, 0), -1), ((69, 0), 1), ((21, 2), -1), ((64, 2), 1), ((48, 1), 1), ((76, 4), 1), ((97, 1), -1), ((15, 1), -1), ((99, 3), -1), ((96, 2), 0), ((60, 3), 0), ((45, 3), -2), ((49, 0), -2), ((74, 2), 1), ((86, 3), -1), ((47, 0), -2), ((58, 4), -2), ((53, 1), -2), ((68, 4), 1), ((82, 4), 0), ((22, 4), 1), ((21, 4), -2), ((8, 2), 0), ((35, 4), -1), ((85, 3), -1), ((86, 3), -2), ((74, 2), 0), ((65, 4), 0), ((66, 0), -1), ((84, 1), 0), ((43, 4), 1), ((70, 2), 0), ((81, 0), 1), ((8, 0), -1), ((61, 3), -1)], 61, 2), ([((47, 4), -2), ((46, 0), 1), ((49, 4), 1), ((81, 1), 0), ((31, 3), 0), ((85, 2), 0), ((85, 3), 1), ((86, 0), -2), ((53, 0), -2), ((29, 4), 0), ((51, 3), 0), ((3, 2), -2), ((54, 0), 1), ((68, 0), 0), ((37, 3), -1), ((91, 3), -1), ((29, 0), 1), ((87, 0), 0), ((47, 0), 0), ((41, 4), -1), ((26, 3), 1), ((27, 0), -1), ((85, 0), 1), ((14, 0), 0), ((69, 3), -2), ((75, 1), 1), ((15, 2), 1), ((5, 1), -1), ((7, 2), 0), ((77, 2), 1), ((38, 1), -1), ((73, 4), 0), ((62, 0), -1), ((91, 0), -1), ((96, 3), -1), ((6, 2), 1), ((56, 2), 0), ((85, 0), 1), ((87, 1), 1), ((56, 3), -1), ((88, 3), -1), ((73, 4), 0), ((46, 0), 1), ((56, 4), 1), ((24, 1), -2), ((10, 0), -1), ((9, 1), -2), ((64, 0), 0), ((21, 0), -1), ((18, 0), -2), ((67, 3), 0), ((41, 0), -1), ((31, 1), 1), ((34, 3), -1), ((74, 1), 1), ((99, 3), 1), ((0, 4), 0), ((22, 4), -1), ((70, 2), 1), ((5, 3), -1), ((28, 2), 0)], 57, 4), ([((87, 2), -1), ((81, 0), -1), ((8, 0), 0), ((67, 3), -1), ((47, 4), 1), ((83, 0), 1), ((65, 4), 1), ((4, 4), 1), ((84, 3), 0), ((76, 0), -1), ((47, 3), 0), ((88, 4), 0), ((59, 4), 1), ((83, 3), -2), ((55, 3), -1), ((48, 3), 1), ((5, 4), -1), ((52, 4), 1), ((58, 2), -2), ((12, 4), -1), ((51, 3), 0), ((37, 0), 0), ((26, 3), 1), ((86, 4), -1), ((45, 2), -1), ((1, 1), 0), ((19, 2), -1), ((97, 3), -2), ((4, 4), -1), ((0, 4), 1), ((60, 1), -1), ((85, 4), 0), ((26, 3), -1), ((31, 2), -1), ((4, 2), -2), ((57, 4), 0), ((90, 1), -2), ((22, 0), 1), ((30, 4), -2), ((99, 2), 0), ((34, 1), 0), ((79, 4), -1), ((82, 0), -1), ((63, 0), -1), ((97, 4), -1), ((61, 1), 0), ((98, 1), -1), ((36, 2), 0), ((36, 2), 1), ((19, 0), -1), ((51, 2), -2), ((8, 1), 0), ((79, 2), -2), ((3, 3), 0)], 58, 3), ([((28, 0), -2), ((95, 1), 1), ((73, 2), -2), ((95, 4), -2), ((24, 4), -1), ((49, 0), -2), ((36, 4), -1), ((60, 1), -2), ((43, 2), 1), ((22, 0), 1), ((65, 1), 1), ((8, 3), 1), ((60, 3), -2), ((18, 0), -2), ((92, 3), -1), ((73, 2), 0), ((40, 4), -2), ((13, 0), -1), ((86, 4), -1), ((0, 4), 0), ((56, 3), -2), ((95, 4), 1), ((51, 3), 1), ((79, 0), -1), ((83, 2), 1), ((90, 0), 0), ((72, 4), -2), ((10, 3), -2), ((35, 2), -1), ((13, 1), -2), ((56, 3), -1), ((88, 2), -2), ((58, 2), -1), ((37, 0), 1), ((50, 0), -2), ((68, 4), -1)], 84, 4), ([((16, 2), 1), ((41, 4), 0), ((43, 4), -1), ((58, 0), 1), ((78, 0), 0), ((33, 0), -1), ((93, 2), -2), ((78, 2), 0), ((55, 1), 1), ((71, 3), 0), ((6, 1), 0), ((21, 4), 1), ((46, 2), 0), ((97, 3), 0), ((45, 2), 0), ((47, 1), -1), ((60, 0), 1), ((54, 4), 1), ((34, 2), -1), ((4, 2), 0), ((11, 3), -2), ((75, 1), 1), ((70, 3), -1), ((66, 4), 0), ((0, 0), 0), ((19, 0), -1), ((88, 3), -2), ((11, 0), -1), ((96, 1), 0), ((24, 0), 0), ((87, 0), -1), ((4, 0), 1), ((80, 4), 1), ((23, 1), 1), ((41, 3), -1), ((85, 3), -1), ((11, 0), 0), ((18, 3), -2), ((53, 0), 1), ((48, 2), -2), ((32, 0), 0), ((33, 4), 0), ((57, 3), 1), ((6, 0), -2), ((58, 3), -1), ((38, 3), 0), ((98, 1), 1), ((16, 2), 1), ((89, 4), -2), ((39, 2), -1), ((34, 1), 1), ((87, 4), 0), ((94, 3), 1), ((87, 0), 1), ((24, 4), 0), ((77, 4), -1), ((63, 2), 1), ((57, 2), 0), ((33, 3), 1), ((48, 1), -2), ((84, 0), 1), ((21, 3), 0), ((2, 1), -1), ((28, 0), -1), ((51, 2), 1), ((43, 2), 0), ((58, 3), -1), ((91, 1), -1), ((83, 1), 0), ((30, 1), 0), ((15, 2), 0), ((84, 1), -1), ((49, 1), 0), ((19, 2), -2), ((66, 2), 1), ((93, 0), 1), ((8, 0), -2), ((31, 2), -2), ((70, 2), -2), ((71, 0), -2), ((85, 0), -2), ((26, 3), 1), ((61, 4), -2), ((74, 2), 0), ((49, 3), -2), ((46, 1), -1), ((85, 1), 0), ((85, 2), -1), ((60, 3), -2), ((18, 0), 1), ((82, 1), -1), ((36, 4), -1), ((98, 0), 1), ((76, 4), -2), ((27, 1), 1), ((88, 3), 0), ((95, 1), -2), ((6, 1), -2), ((73, 4), -2), ((17, 4), -2), ((25, 3), -2), ((34, 1), -1), ((24, 0), -2), ((11, 3), -1), ((25, 4), 0), ((77, 4), 1), ((69, 1), 1), ((34, 2), -2), ((37, 1), 1), ((43, 3), -2), ((95, 2), 0), ((72, 4), 0), ((34, 4), -2), ((74, 4), 0), ((42, 4), 0), ((61, 2), -1)], 38, 4), ([((90, 4), -1), ((98, 0), 1), ((37, 1), 0)], 7, 0), ([((5, 1), 1), ((41, 0), -2), ((29, 4), 1), ((74, 2), -1), ((86, 0), -2), ((94, 3), -2), ((42, 1), -2), ((70, 2), -1), ((66, 0), 1), ((46, 2), -2), ((16, 3), -2), ((36, 0), 0), ((28, 2), 1), ((19, 3), 0), ((62, 1), 0), ((27, 1), 0), ((38, 1), -2), ((84, 3), 1), ((45, 3), 0), ((40, 1), -2), ((60, 2), 1), ((73, 2), -1), ((43, 0), 1), ((89, 4), 0), ((21, 1), -2), ((10, 1), -1), ((37, 3), 1), ((97, 1), 0), ((25, 0), -2), ((8, 1), 0), ((99, 4), 1), ((78, 2), -2), ((21, 3), -2), ((71, 1), 0), ((77, 3), 1), ((92, 2), 0), ((31, 0), 1), ((33, 0), -2), ((80, 2), 0), ((72, 0), 1), ((63, 0), -1), ((14, 0), 0), ((95, 0), -2), ((48, 3), -1), ((26, 2), -2), ((72, 1), 1), ((93, 0), -1), ((12, 1), -1), ((24, 2), 1), ((60, 3), 0), ((93, 3), -1), ((84, 4), 0), ((54, 3), 1), ((55, 2), 0), ((1, 0), -1), ((48, 3), 1), ((39, 0), -1), ((56, 4), 1), ((89, 2), 0), ((76, 1), -1), ((73, 4), -2), ((20, 4), -1), ((66, 4), -1), ((29, 1), 1), ((63, 2), 1), ((15, 0), 1), ((16, 0), 1), ((25, 4), -1), ((45, 0), -2), ((5, 3), 0), ((42, 2), -1), ((4, 2), -2), ((88, 2), -1), ((60, 3), 1), ((30, 2), -2), ((53, 3), -1), ((19, 4), 1), ((93, 4), 0), ((4, 3), -1), ((68, 2), -1), ((78, 4), 0), ((82, 0), 0), ((17, 3), 0), ((3, 4), 1), ((97, 1), -1), ((56, 4), 0), ((87, 3), -2), ((2, 3), 0), ((76, 3), 0), ((14, 2), 1), ((34, 2), 0), ((65, 0), 1), ((11, 2), 1), ((68, 1), 1), ((82, 1), 0), ((95, 3), 0), ((50, 0), 1), ((69, 3), 0), ((45, 0), -1), ((89, 2), -2), ((31, 0), -1), ((81, 3), 0), ((41, 0), -2), ((3, 3), -1), ((41, 2), 0), ((94, 0), 0), ((79, 3), 1), ((67, 1), -1), ((48, 0), -1), ((73, 3), -1), ((38, 2), 0), ((39, 1), -2), ((20, 4), -2), ((69, 4), 1), ((15, 2), -2), ((32, 1), -2), ((26, 3), -2), ((21, 3), 1), ((98, 4), 1), ((0, 2), 0), ((13, 1), -2), ((99, 4), -1), ((52, 1), -1), ((78, 3), 0), ((95, 2), 1), ((46, 1), 1), ((38, 3), 1), ((15, 3), -1), ((21, 3), 1), ((47, 1), 1), ((95, 2), 1), ((4, 0), 0), ((74, 2), 0), ((51, 2), -1), ((94, 2), 1), ((18, 4), 1), ((53, 1), -2), ((16, 0), 0), ((69, 1), -2), ((47, 4), -2), ((29, 0), -2), ((69, 2), 0), ((53, 4), 1), ((61, 0), 0), ((2, 0), 1), ((12, 1), 1), ((60, 3), 1), ((83, 2), -2), ((85, 4), 1), ((66, 1), -1), ((61, 1), -1), ((16, 1), -1), ((86, 3), 0), ((36, 2), -1), ((95, 3), -2), ((37, 1), -1), ((26, 0), -2), ((21, 3), 1), ((97, 4), -1), ((64, 3), 1), ((98, 0), -1), ((68, 3), 1), ((12, 0), -2), ((88, 1), -2), ((2, 0), 0), ((1, 0), 0), ((7, 4), -1), ((24, 2), 0), ((17, 1), -1), ((5, 3), -1), ((91, 3), -1), ((35, 3), -2), ((61, 1), -2), ((43, 1), 1), ((53, 3), -1), ((9, 3), -1), ((55, 0), -1), ((75, 4), 0), ((64, 3), -1), ((37, 4), -1), ((80, 1), 0), ((66, 2), -1), ((50, 3), 0), ((84, 2), -1), ((70, 4), -2)], 32, 4), ([((38, 0), -2), ((35, 0), -1), ((79, 1), -1), ((19, 1), 1), ((44, 1), 0), ((24, 1), -1), ((22, 0), 1), ((76, 0), -2), ((97, 0), -2), ((28, 1), 1), ((1, 3), 0), ((47, 2), -2), ((77, 2), -1), ((29, 2), 0), ((17, 2), 1), ((24, 2), -1), ((79, 2), -2), ((65, 0), 1), ((0, 0), 1), ((38, 1), 1), ((32, 0), -1), ((61, 0), -1), ((82, 0), -2), ((58, 2), 0), ((97, 4), 1), ((67, 1), 1), ((12, 3), 1), ((12, 0), -2), ((64, 2), 0), ((25, 0), -2), ((13, 1), 0), ((92, 3), 0), ((11, 3), 1), ((46, 2), 0), ((81, 4), 1), ((58, 2), -2), ((38, 4), -1), ((49, 3), 1), ((16, 0), 0), ((28, 0), -2), ((44, 0), 1), ((23, 3), 0), ((76, 4), -2), ((11, 3), -1), ((2, 4), -2), ((54, 2), 0), ((16, 2), 1), ((65, 2), -2), ((82, 1), 1), ((1, 4), 1), ((20, 4), -1), ((49, 4), -1), ((69, 2), -2), ((37, 0), -1), ((72, 3), -2), ((63, 3), -1), ((97, 1), -1), ((88, 3), 0), ((70, 3), -1), ((47, 0), -2), ((59, 3), 0), ((1, 0), -1), ((5, 3), 1), ((15, 4), 0), ((3, 2), -1), ((66, 4), -1), ((72, 3), -2), ((64, 2), -1), ((52, 4), 0), ((13, 0), -2), ((97, 4), -2), ((67, 2), 0), ((88, 2), -1), ((85, 2), -1), ((21, 1), 1), ((14, 3), 1), ((0, 1), 1), ((64, 1), -1), ((62, 0), 0), ((36, 2), -2), ((28, 3), -2), ((24, 3), -2), ((58, 0), -1), ((96, 1), -1), ((97, 0), -1), ((7, 2), 1), ((46, 1), 1), ((11, 0), 0), ((51, 1), -2), ((25, 1), 0), ((8, 4), 0), ((53, 3), -1), ((91, 2), -2), ((76, 0), -1), ((48, 2), 0), ((30, 1), 1), ((72, 1), 1), ((93, 0), 0), ((53, 0), -2), ((55, 1), 0), ((84, 0), -1), ((62, 4), -2), ((94, 3), 0), ((37, 0), 1), ((56, 0), -2), ((45, 2), -1), ((22, 4), -2), ((83, 1), -1), ((76, 2), 0), ((36, 1), -2), ((83, 2), -1), ((85, 3), 1), ((22, 2), -2), ((76, 1), -2), ((85, 1), -1), ((69, 4), 1), ((9, 3), 0), ((16, 2), 1), ((40, 0), -2), ((5, 2), -1), ((98, 4), -1), ((79, 1), 0), ((55, 0), -2), ((3, 4), -2), ((50, 1), 1), ((73, 3), -2), ((50, 1), -1), ((34, 2), 0), ((63, 3), -1), ((88, 2), 0), ((36, 1), 1), ((61, 4), -1), ((72, 0), 1), ((99, 3), -1), ((21, 1), 1), ((95, 1), 0), ((17, 1), 0), ((79, 2), 1), ((5, 1), 1), ((26, 0), 0), ((59, 0), -1), ((30, 2), 0), ((21, 4), -2), ((13, 3), 0), ((69, 1), -2), ((93, 4), -2), ((68, 3), 1), ((50, 3), -2), ((50, 4), 0), ((57, 0), -1), ((86, 2), -1), ((65, 3), -2), ((57, 2), 0), ((64, 3), -1), ((9, 2), -1), ((66, 0), -1), ((87, 1), -1), ((45, 3), 1), ((89, 2), -2), ((46, 3), 0), ((58, 4), 1), ((98, 1), -2), ((56, 3), -2), ((52, 0), -1), ((40, 4), 0), ((30, 3), -1), ((34, 4), -1), ((65, 0), 0), ((1, 3), 1), ((91, 0), -2), ((21, 4), 1), ((63, 0), 1), ((57, 0), 1), ((25, 1), -2), ((30, 0), -1), ((30, 2), 1), ((56, 4), -1), ((8, 4), -2), ((87, 0), -2), ((99, 0), 0), ((59, 4), -2), ((24, 4), 1), ((61, 3), -2), ((3, 3), 0), ((72, 4), -1), ((47, 4), 0), ((18, 0), 1), ((4, 3), 0), ((16, 4), -2), ((9, 1), 1), ((65, 3), -2), ((76, 2), -2), ((83, 2), -1), ((88, 2), 0), ((85, 0), -1), ((75, 1), 1), ((97, 0), -1), ((32, 0), 0), ((67, 4), -1), ((42, 3), -1), ((64, 1), -2), ((64, 2), 0), ((6, 2), -1), ((96, 2), 1), ((75, 4), -1), ((73, 3), -1), ((16, 3), 1), ((3, 1), 0), ((50, 0), 1), ((80, 2), 0), ((63, 1), -2), ((85, 0), 1), ((65, 1), -1), ((24, 3), -2), ((97, 0), -1), ((45, 0), -1), ((79, 3), -1), ((27, 3), -1), ((52, 1), -2), ((1, 0), 1), ((85, 2), -2), ((0, 2), -2), ((55, 3), -2), ((9, 3), 1), ((78, 0), -1), ((53, 0), -1), ((68, 2), -1), ((47, 1), -2), ((60, 2), -2), ((70, 4), 1), ((1, 2), -1), ((50, 0), -1), ((76, 3), -2), ((46, 0), 1)], 49, 4), ([((34, 2), -2), ((65, 4), -2), ((70, 4), -2), ((26, 1), 1), ((4, 2), 1), ((50, 1), -1), ((1, 1), 1), ((8, 1), 0), ((44, 3), -2), ((31, 1), -1), ((25, 0), -1), ((34, 2), -2), ((22, 1), 0), ((25, 4), 0), ((79, 1), -1), ((36, 4), -2), ((87, 3), 0), ((43, 0), 0), ((96, 3), -2), ((53, 1), -2), ((46, 0), -2), ((55, 1), 0), ((2, 1), -2), ((48, 2), 1), ((94, 1), -2), ((71, 3), -2), ((72, 1), -2), ((13, 2), -1), ((46, 4), 1), ((87, 3), -2), ((70, 2), -1), ((60, 4), 0), ((9, 1), 0), ((20, 4), 1), ((26, 2), -1), ((89, 2), -1), ((99, 4), -2), ((85, 3), -2), ((37, 3), -2), ((17, 0), -2), ((53, 3), -2), ((53, 4), -1), ((76, 1), -1), ((68, 3), 0), ((38, 1), -2), ((25, 0), 0), ((85, 1), 1), ((17, 4), 0), ((15, 1), -2), ((6, 1), -1), ((7, 0), -2), ((19, 0), -1), ((18, 3), -2), ((53, 1), 0), ((4, 0), 0), ((55, 4), 0), ((50, 1), 1), ((7, 4), 0), ((60, 4), -1), ((17, 0), 0), ((11, 1), -2), ((45, 3), -2), ((72, 2), -1), ((97, 1), -1), ((68, 0), -1), ((27, 4), -1), ((79, 1), 0), ((57, 1), -1), ((97, 0), -1), ((95, 2), 1), ((17, 1), -2), ((1, 2), 1), ((48, 1), -2), ((17, 3), -1), ((60, 0), -1), ((49, 1), -1), ((27, 3), -1), ((92, 0), 0), ((21, 0), -1), ((72, 0), -1), ((26, 0), 1), ((61, 2), 0), ((66, 4), -1), ((13, 4), -2), ((0, 2), -1), ((2, 1), -2), ((63, 2), -2), ((55, 1), 0), ((2, 0), 1), ((77, 4), -2), ((7, 4), 1), ((73, 2), 1), ((72, 3), 0), ((71, 3), 1), ((3, 1), 1), ((79, 1), -2), ((91, 1), -2), ((36, 0), -1), ((7, 4), -2), ((9, 3), 0), ((8, 2), -2), ((1, 1), 0), ((55, 3), -2), ((42, 4), -1), ((23, 0), 1), ((60, 0), 1), ((79, 1), -1), ((86, 2), 0), ((28, 2), -2), ((10, 0), 1), ((91, 1), -1), ((89, 3), -1), ((46, 4), 0), ((81, 4), -1), ((23, 3), -2), ((40, 1), -2), ((91, 3), -1), ((81, 4), -1), ((22, 1), 0), ((4, 3), -1), ((94, 1), -2), ((72, 3), -1), ((54, 4), 1), ((72, 2), -2), ((5, 3), 1), ((77, 2), -1), ((27, 2), 1), ((83, 3), -1), ((85, 0), -2), ((28, 2), -1), ((29, 1), 0), ((99, 0), 0), ((73, 2), -2), ((68, 2), 1), ((15, 1), 1), ((58, 1), -1), ((33, 1), -1), ((66, 1), -1), ((6, 3), -1), ((67, 0), -1), ((79, 0), -1), ((40, 3), 1), ((99, 4), -1), ((23, 0), -2), ((91, 2), 0), ((55, 0), 1), ((36, 1), 0), ((95, 1), -1), ((78, 4), -2), ((73, 2), -1), ((69, 4), -2), ((59, 2), -1), ((65, 0), -2), ((42, 3), -1), ((84, 3), -1), ((57, 1), 1), ((9, 4), -1), ((1, 3), -2), ((21, 2), 1), ((76, 0), -1), ((56, 2), 0), ((86, 0), 0), ((52, 3), -1), ((89, 4), 1), ((6, 0), -1), ((52, 4), -2), ((9, 3), -2), ((60, 3), 0), ((53, 1), 0), ((4, 1), 0), ((87, 4), 1), ((19, 1), -2), ((67, 2), -1), ((35, 4), -2), ((31, 1), -1), ((1, 3), -2), ((48, 4), 0), ((79, 2), -1), ((24, 1), -2), ((54, 0), -1), ((63, 2), -1), ((74, 2), -1), ((66, 0), 1), ((20, 3), 0), ((23, 0), 0), ((54, 0), 1), ((26, 1), -2), ((36, 0), -2), ((17, 1), 1), ((10, 2), 1), ((12, 1), 0), ((58, 4), -2), ((83, 0), 1), ((70, 0), -1), ((56, 1), -1), ((33, 1), 1), ((16, 3), -1), ((81, 4), -2), ((69, 3), 1), ((11, 0), 1), ((27, 3), -2), ((27, 4), -2), ((1, 3), -2), ((35, 3), -2), ((25, 0), -1), ((31, 1), 0), ((18, 3), 1), ((58, 3), -2), ((16, 2), 0), ((10, 4), -1), ((98, 0), 0), ((26, 2), -2), ((55, 4), 1), ((30, 1), 0), ((56, 2), 0), ((12, 3), 0), ((37, 4), 1), ((66, 1), 1), ((76, 2), 0), ((55, 3), -1), ((45, 4), -1), ((4, 1), -2), ((90, 0), -2), ((12, 3), 1), ((42, 1), -2), ((35, 1), 0), ((5, 2), 1), ((22, 1), 1), ((19, 3), -2), ((5, 4), 0), ((80, 4), 0), ((21, 1), 0), ((51, 2), -1), ((84, 1), -2), ((97, 3), 1), ((23, 0), 1), ((94, 1), -2), ((38, 0), -2), ((9, 1), -2), ((74, 4), 1), ((26, 3), -2), ((57, 2), 0), ((6, 1), 0), ((74, 3), -2), ((31, 3), -2), ((71, 3), -1), ((99, 1), -1), ((74, 4), 1), ((90, 3), -2), ((91, 4), 0), ((3, 3), -2), ((4, 4), 0), ((5, 3), 0), ((9, 0), -2), ((78, 4), 0), ((24, 3), -2)], 71, 0)], seed = 1387633884765120387
cc 19cc85ba951577f4bccf26eee443b4fd546a3a36beba36b818ea8311580d7c1f # shrinks to batches = [([((40, 1), -1), ((20, 3), 1), ((85, 0), 1), ((89, 3), 0), ((76, 2), 0), ((10, 0), -1), ((42, 4), 0), ((61, 0), 0), ((41, 2), 0), ((69, 1), -2), ((80, 3), -2), ((73, 4), -1), ((85, 2), -1), ((29, 0), -2), ((79, 0), -2), ((68, 1), 1), ((82, 2), -1), ((94, 3), -2), ((36, 2), -1), ((67, 4), 0), ((76, 4), 1), ((55, 4), 1), ((38, 2), -2), ((75, 3), -1), ((41, 1), 0), ((7, 0), -2), ((8, 3), -1), ((0, 2), 1), ((13, 2), 1), ((17, 1), 1), ((54, 3), 1), ((3, 4), 0), ((26, 3), 1), ((48, 2), -1), ((51, 3), -1), ((34, 2), 1), ((4, 4), 1), ((84, 4), -2), ((3, 3), -1), ((18, 2), -1), ((44, 1), -2), ((8, 1), -1), ((91, 4), 0), ((4, 2), 0), ((53, 4), -1), ((89, 4), -1), ((24, 1), -1), ((53, 4), 0), ((40, 2), -1), ((92, 0), 1), ((59, 3), -2), ((23, 2), -1), ((43, 3), 0), ((9, 0), 1), ((14, 3), -2), ((92, 3), -2), ((37, 1), -2), ((12, 2), 1), ((46, 3), 0), ((47, 2), -1), ((17, 1), -2), ((99, 0), 1), ((50, 2), 0), ((38, 1), -1), ((34, 0), -1), ((15, 2), -1), ((9, 4), 1), ((81, 3), 1), ((19, 1), -2), ((81, 3), -1), ((24, 3), -1), ((31, 3), -1), ((48, 0), -1), ((97, 2), -1), ((73, 0), 1), ((19, 3), -1), ((85, 3), 1), ((59, 4), 1), ((23, 1), 0), ((0, 1), 1), ((3, 1), 1), ((47, 0), 0), ((93, 4), 1), ((94, 3), -1), ((22, 1), -2), ((51, 0), 1), ((13, 3), 0), ((26, 0), -1), ((89, 0), -1), ((70, 0), 0), ((52, 1), -1)], 91, 3), ([((98, 3), 0), ((76, 2), 0), ((4, 3), 0), ((28, 0), -1), ((43, 0), 1), ((12, 3), 1), ((5, 1), -1), ((11, 3), -1), ((97, 3), -1), ((3, 4), -2), ((50, 2), 0), ((99, 4), 0), ((24, 1), 0), ((34, 4), -2), ((16, 0), -1), ((42, 1), 1), ((81, 4), 0), ((30, 1), -1), ((61, 3), -2), ((13, 3), 1), ((48, 4), 1), ((62, 1), 1), ((64, 1), 0), ((63, 1), -2), ((93, 4), -2), ((7, 0), -2), ((7, 3), 1), ((58, 4), -1), ((82, 3), 1), ((48, 2), 0), ((31, 2), 0), ((51, 4), 1), ((30, 4), 0), ((90, 1), -1), ((52, 3), -1), ((48, 0), 1), ((13, 0), -1), ((78, 2), -2), ((50, 0), -1), ((65, 3), -2), ((94, 1), 1), ((43, 2), 0), ((1, 3), 1), ((2, 4), -2), ((60, 4), -1), ((43, 2), 1), ((13, 0), -1), ((45, 2), 0), ((60, 3), -1), ((19, 4), -1), ((87, 2), -2), ((67, 2), -2), ((88, 1), 1), ((84, 0), 1), ((82, 4), -2), ((2, 1), 1), ((52, 3), -2), ((39, 0), 0), ((63, 4), -2), ((74, 3), 0), ((35, 2), 1), ((0, 1), -1), ((94, 3), -2), ((27, 4), 1), ((17, 0), -2), ((20, 0), -2), ((37, 0), -2), ((34, 3), 0), ((47, 2), -1), ((68, 4), -2), ((45, 2), 1), ((50, 2), -2), ((16, 4), -1), ((37, 3), 1), ((75, 2), 0)], 37, 1), ([((15, 4), -2), ((13, 3), -1), ((94, 2), -2), ((23, 1), 1), ((64, 0), 0), ((80, 3), 1), ((23, 0), -1), ((19, 2), -1), ((70, 3), 1), ((44, 3), -1), ((25, 3), -1), ((72, 2), -2), ((21, 3), 0), ((54, 1), -1), ((74, 2), 0), ((29, 0), -2), ((47, 3), 0), ((20, 4), -2), ((8, 3), 1), ((67, 3), 1), ((77, 3), -2), ((14, 1), 0), ((47, 0), 0), ((64, 3), -2), ((46, 2), 1), ((36, 4), -1), ((64, 3), 1), ((73, 1), 0), ((15, 0), -1), ((44, 3), 1), ((47, 1), -1), ((91, 4), 1), ((8, 0), 0), ((34, 3), 0), ((47, 2), 1), ((79, 2), -1), ((36, 4), -1), ((21, 0), 0), ((2, 4), -2), ((78, 0), 0), ((6, 1), -2), ((7, 0), -1), ((76, 0), -1), ((26, 0), 0), ((55, 0), 0), ((76, 2), -2), ((34, 0), -2), ((89, 0), 1), ((58, 4), -2), ((31, 3), 1), ((20, 3), -2), ((35, 0), 1), ((17, 4), 0), ((23, 0), 1), ((32, 4), -1), ((29, 0), 0), ((20, 1), -2), ((31, 1), 1), ((66, 2), -2), ((24, 1), 1), ((79, 0), 0)], 1, 4), ([((30, 4), -2), ((70, 1), 1), ((83, 2), 1), ((27, 2), -1), ((74, 3), 0), ((35, 2), 1), ((33, 0), 1), ((68, 1), -2), ((43, 0), 1), ((14, 0), 1), ((57, 0), -1), ((65, 3), -1), ((24, 0), 0), ((15, 4), 0), ((68, 0), 0), ((24, 1), 0), ((59, 3), -2), ((97, 3), 1), ((49, 0), -2), ((65, 3), -2), ((87, 4), -1), ((31, 0), -1), ((7, 3), -2), ((63, 3), 0), ((3, 0), 0), ((51, 1), -1), ((71, 1), 1), ((30, 0), 1), ((52, 4), -2), ((10, 2), 0), ((91, 2), -1), ((36, 3), 0), ((11, 0), -1), ((99, 3), -1), ((45, 2), 1), ((0, 1), -1), ((9, 1), 1), ((95, 4), 0), ((41, 2), -2), ((28, 0), -2), ((1, 1), -1), ((93, 1), -2), ((45, 1), -1), ((26, 3), -2), ((12, 2), -2), ((30, 0), 0), ((92, 3), -1), ((92, 0), -2), ((22, 2), -1), ((79, 2), -1), ((6, 2), 0), ((99, 3), 1), ((76, 1), -1), ((91, 2), 0), ((25, 3), -1), ((63, 1), 0), ((24, 4), 0), ((87, 2), -1)], 18, 1), ([((68, 3), 0), ((69, 3), 1), ((66, 1), -1), ((20, 1), -2), ((72, 2), -1), ((5, 2), -1), ((65, 2), -2), ((49, 2), -2), ((19, 3), 0), ((63, 1), -1), ((41, 3), 0), ((51, 3), -1), ((97, 3), 1), ((49, 4), -1), ((54, 4), 1), ((79, 2), 0), ((5, 4), -1), ((98, 0), -2), ((79, 3), 1), ((88, 0), 0), ((20, 3), 1), ((43, 0), 1), ((87, 2), 1), ((99, 3), -2), ((30, 3), -2), ((90, 1), -1), ((44, 0), 1), ((81, 1), 1), ((13, 0), 0), ((20, 0), -2), ((18, 1), -1), ((7, 1), 1), ((12, 1), 1), ((94, 0), 0), ((43, 1), 1), ((78, 0), 1), ((45, 0), -2), ((38, 4), -1), ((23, 0), -1), ((47, 4), 0), ((59, 3), -2), ((11, 1), 0), ((73, 1), 1), ((17, 4), -1), ((28, 0), 0), ((8, 4), -1), ((81, 2), -2), ((38, 0), 0), ((35, 2), 0), ((3, 4), -2), ((80, 2), 1), ((86, 0), 0), ((31, 4), -2), ((37, 4), 1), ((58, 1), -2), ((12, 2), -2), ((71, 0), 1), ((14, 4), 1), ((74, 3), 1), ((51, 3), 0), ((17, 0), -1), ((12, 0), 0), ((74, 0), -2), ((94, 2), -2), ((70, 0), 0), ((61, 4), -2), ((57, 1), 0), ((86, 0), -1), ((48, 4), 1), ((26, 2), 1), ((3, 2), 1), ((94, 0), 1), ((59, 2), -2), ((27, 1), 0), ((82, 4), -2), ((12, 0), 1), ((20, 0), -1), ((26, 2), 0), ((6, 3), -2), ((49, 1), -1), ((30, 2), -1), ((93, 3), -2)], 76, 1), ([((24, 0), 1), ((8, 2), 0), ((49, 1), -1), ((16, 1), 0), ((55, 1), -1), ((60, 3), 0), ((75, 2), -2), ((65, 3), -2), ((83, 2), -2), ((38, 1), -2), ((11, 2), -1), ((37, 2), -1), ((24, 2), 0), ((92, 4), 1), ((59, 3), 0), ((40, 4), -1), ((2, 3), 0), ((15, 1), 1), ((52, 2), 1), ((54, 2), 0), ((59, 1), 0), ((94, 4), -2), ((39, 0), 1), ((45, 3), -2), ((19, 4), 0), ((82, 2), 0), ((63, 3), -2), ((61, 3), 1)], 4, 2), ([((7, 3), 0), ((54, 0), -1), ((50, 1), -2), ((2, 0), -2), ((27, 2), -2), ((86, 3), -1), ((47, 0), -1), ((1, 1), 1), ((1, 1), 1), ((80, 4), -1), ((48, 1), -1), ((37, 0), -2), ((13, 0), -2), ((95, 4), -2)], 26, 3), ([((57, 1), 1), ((58, 1), -2), ((85, 0), -1), ((34, 0), -2), ((24, 0), -1), ((95, 4), 1), ((57, 1), -1), ((13, 3), 1), ((61, 1), -2), ((78, 3), -2), ((7, 3), 0), ((32, 1), 1), ((76, 0), 1), ((89, 4), 1), ((29, 1), 0), ((44, 3), -2), ((9, 1), -1), ((9, 2), 0), ((56, 3), 1), ((23, 3), 0), ((87, 2), -1), ((74, 3), -2), ((49, 1), 0), ((46, 2), 0), ((35, 3), 0), ((61, 3), -1), ((22, 4), 1), ((6, 3), 1), ((35, 0), 0), ((9, 0), 1), ((15, 0), 1), ((76, 0), 1), ((90, 1), 0), ((95, 2), 1), ((36, 2), -2), ((40, 4), 0), ((53, 3), 0), ((12, 2), 1), ((66, 1), -2), ((12, 3), 1), ((92, 4), 0), ((3, 4), -1), ((8, 1), -1), ((78, 4), -2), ((91, 0), -2), ((11, 0), -1), ((23, 0), -1), ((92, 4), -2), ((32, 2), -1)], 76, 2), ([((57, 0), -2), ((90, 4), -1), ((82, 3), -2), ((89, 4), -1), ((26, 0), 0), ((61, 4), 0), ((48, 4), -1), ((53, 1), 0), ((74, 0), 1), ((77, 0), 1), ((53, 2), 0), ((6, 3), -1), ((35, 1), -2), ((21, 4), 0), ((72, 0), 1), ((53, 2), 1), ((85, 1), -2), ((48, 4), 1), ((14, 2), -1), ((98, 3), -2), ((13, 4), 0), ((8, 3), -2), ((67, 2), 1), ((45, 0), 1), ((30, 1), 1), ((58, 1), -1), ((44, 4), 0), ((79, 1), 0), ((90, 0), 0), ((77, 4), 1), ((37, 1), -1), ((58, 3), -1), ((39, 3), -2), ((34, 1), -1), ((88, 0), 1)], 5, 4)], seed = 5287837501273515804
cc ddb49d3ed38dd8e40d414539257775492e9e44bd52ea8a8db9b56420ffee22a5
cc ba3e39fad39ccd114ac663b9327ce3476adaaccf061da41d1af7c79c4057f544 # shrinks to batches = [([], 2), ([(0, -1)], 0)], seed = 0
cc bf12be9ee51ebf2b5ace02e4bcd6f41d4d950b1199e2df570a02c40a5185b849 # shrinks to batches = [([((0, 3), -1)], 0, 0), ([((0, 3), 1), ((1, 0), -1)], 0, 0)], seed = 0
cc 73a81a30deb8c87a4de58c5171cace5ee825763abae1670dabe1896ba8200820 # shrinks to batches = [([((0, 4), -1)], 0, 0), ([((0, 4), 1), ((1, 0), -1)], 0, 0)], seed = 0
cc 5c8af305c5969080719f120029bb8d21748f96b8b9dde796bb3db798bfec7d98 # shrinks to batches = [([((0, 1), -1)], 0, 0), ([((0, 1), 1), ((1, 0), -1)], 0, 0)], seed = 0
