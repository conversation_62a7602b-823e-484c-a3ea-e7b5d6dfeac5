# Seeds for failure cases proptest has generated in the past. It is
# automatically read and these particular cases re-run before any
# novel cases are generated.
#
# It is recommended to check this file in to source control so that
# everyone who runs the test benefits from these saved cases.
cc 062fdfd200ed6c914f1700833a5f3def3b6afa6e1b71059080d325a6d8576251 # shrinks to batches = [([(0, -1), (1, 1), (2, 1)], 0)], seed = 0
cc 9a8ca39b89d5c7315fa19dcf45fdb94c5c57eb3c084ba28a7998a300bf803562 # shrinks to batches = [([((0, 0), -1), ((0, 1), 1), ((1, 0), -1)], 0, 0)], seed = 0
cc 4e4a531d0b7f32296b0c34189dd5450a83e44861d95b48c81c49822705bc29be # shrinks to batches = [([((0, 0), -1), ((0, 1), -1), ((0, 2), -1)], 0, 0)], seed = 0
cc 7b4811ab497cd82804bbdd5d0840e3e192224ec1ac5d0b5068980b83570a8f22 # shrinks to batches = [([(0, 1), (1, -1), (2, -1)], 0)], seed = 0
cc 14e6412aa05a154e0381563b20858e9bd0877a0ba32d2eab6313c1fda4d210cb # shrinks to batches = [([], 7), ([(18, 0), (21, -2), (19, -2), (39, 0), (42, 1), (44, 1), (43, 1), (11, 0), (47, 1), (25, 1), (41, -1), (30, -1), (23, -1), (13, -1), (3, 1), (16, 1), (0, 1), (17, -1), (37, -1), (30, 0), (7, 0), (46, -2), (21, 0), (39, 1), (14, -2), (6, 1), (3, -2), (36, -2), (28, 0), (38, -1)], 3), ([(4, -1), (18, -2), (38, -1), (0, -1), (37, -2), (5, -1), (20, 1), (10, -1), (15, 0), (41, 0), (9, -1), (6, 1), (19, -2), (11, -1), (20, 0), (6, -1), (4, -2), (15, -1), (44, -2), (42, -1), (33, 1), (14, -1), (15, 0), (2, 1), (37, 0), (31, -1), (6, -1), (17, -2), (12, -2), (27, -1), (28, 1), (17, 0), (24, 1), (36, -1), (9, -2), (34, -2), (37, -1), (28, 1), (2, -2), (6, -2), (8, -2), (4, 1), (46, 0), (48, -1), (29, -2), (46, 0), (33, 1), (24, -2), (0, 0), (11, -1), (20, 1), (43, 0), (39, 1), (1, -1), (32, -1), (4, -1), (41, -1), (27, -1), (7, 1), (18, 1), (29, 1), (23, -2), (40, -2), (17, -1), (43, -2), (8, -1), (15, 0), (7, 1), (29, 0), (23, 1), (34, -2), (14, 1), (40, -2), (39, -1), (27, -2), (28, 0), (36, -2), (42, -2), (24, 0), (2, -2), (30, -2), (30, 1), (24, 0), (13, -2), (45, -2), (5, 1), (2, -1), (6, 1), (11, 0), (23, 0), (30, -1), (46, 0)], 8), ([(49, -2), (47, -2), (1, -1), (28, -2), (32, -1), (20, -2), (11, -2), (29, 1), (49, -2)], 12), ([(23, 0), (17, 1), (37, 1), (16, 1), (0, 0), (28, 0), (18, 1), (28, -2), (32, -1), (2, -1), (23, -1), (7, 1), (0, -1), (26, 1), (26, -1), (4, 1), (16, 0)], 13), ([(1, -2), (45, 1), (14, 1), (32, -2), (42, 0), (0, -1), (39, 0), (35, -2), (33, 1), (16, 1), (13, 1), (41, 1), (16, -2), (24, -1), (23, -2), (41, -2), (16, -2), (30, 0), (13, -2), (5, -1), (19, -1)], 12), ([(30, 0), (44, -1), (5, -2), (27, 1), (3, 1), (26, -1), (13, 1), (37, 0), (19, 1), (23, 1), (30, -2), (7, -1), (17, -2), (24, -1), (37, 1), (4, 0), (39, -1), (11, -2), (23, 0), (14, -2), (9, -2), (3, -1), (26, 1), (15, -1), (27, -2), (35, 0), (43, 0), (36, -1), (14, 0), (15, 0), (43, 0), (43, -2), (13, -2), (26, -2), (42, 1), (14, 0), (7, 1), (28, 1), (9, -1), (32, -2), (41, 0), (9, -1), (11, -1), (0, 1), (1, -2), (15, 1), (6, -2), (40, -2), (33, 0), (20, 0), (0, 1), (38, -2), (44, 0), (22, 1), (7, -2), (7, 0), (27, 1), (37, -2), (45, -2), (47, 0), (41, -2), (48, 1), (25, 0)], 21)], seed = 16390902725642585853
